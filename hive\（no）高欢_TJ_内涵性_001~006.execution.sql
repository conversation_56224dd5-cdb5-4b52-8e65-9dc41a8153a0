SET hive.execution.engine = tez;
SET yarn.timeline-service.enabled = true;
SET hive.query.timeout.seconds=3600s;

--JKW-TJ-0001 个人基本信息-受检者唯一编码规范性
with BASE as (
    select
        t1.SJZWYBM,
        t2.ZSSJ,
        t1.YLJGYQDM,
        t1.ZJLX,
        t1.ZJH,
        a.FJ_YLJGYQDM as JGDM
    from hive_jktj_prod_source_dwd.T_PERSONAL_BASE_INFO t1
    left join hive_jktj_prod_source_dwd.T_AUDIT_FINAL_STATUS t2
        on t1.SJZWYBM = t2.SJZWYBM and t1.YLJGYQDM = t2.Y<PERSON>JGYQDM
    left join hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON a
        on t1.yljgyqdm = a.yljgyqdm
    where t2.ZSSJ >= cast(from_unixtime(unix_timestamp('${BTIME}', 'yyyyMMdd')) as timestamp)
      and t2.ZSSJ < cast(from_unixtime(unix_timestamp('${ETIME}', 'yyyyMMdd')) as timestamp)
      and t1.xgbz = '1' and t2.xgbz = '1'
),
grouped as (
    select
        date_format(ZSSJ, 'yyyyMMdd') as YWRQ,
        JGDM,
        ZJLX,
        ZJH,
        count(distinct SJZWYBM) as sjzwybm_cnt,    --不同受检者唯一编码的数量（即唯一性规范性）
        count(*) as cnt
    from base
    group by date_format(ZSSJ, 'yyyyMMdd'), JGDM, ZJLX, ZJH
)
insert into hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
select
    YWRQ,
    JGDM,
    'JKW-TJ-0001' as GZBH,
    sum(cnt) as JYJLSH,
    sum(case when sjzwybm_cnt = 1 then cnt else 0 end) as FHGZSH,
    'v2,0' as BBH,
    current_timestamp() as XGSHJ
from grouped
group by ywrq, JGDM;

--JKW-TJ-0002 个人体检信息-受检者单次体检编码规范性
with BASE as (
    select
        t1.SJZWYBM,
        t2.ZSSJ,
        t1.YLJGYQDM,
        t1.SJTJRQ,
        t1.SJZDCTJBM,
        a.FJ_YLJGYQDM as JGDM
    from hive_jktj_prod_source_dwd.T_PERSONAL_RESERVATION t1
    left join hive_jktj_prod_source_dwd.T_AUDIT_FINAL_STATUS t2
        on t1.SJZWYBM = t2.SJZWYBM and t1.YLJGYQDM = t2.YLJGYQDM
    left join hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON a
        on t1.yljgyqdm = a.yljgyqdm
    where t2.ZSSJ >= cast(from_unixtime(unix_timestamp('${BTIME}', 'yyyyMMdd')) as timestamp)
      and t2.ZSSJ < cast(from_unixtime(unix_timestamp('${ETIME}', 'yyyyMMdd')) as timestamp)
      and t1.xgbz = '1' and t2.xgbz = '1'
),
grouped as (
    select
        date_format(ZSSJ, 'yyyyMMdd') as YWRQ,
        JGDM,
        SJTJRQ,
        count(distinct SJZDCTJBM) as SJZDCTJBM_cnt,    --不同受检者唯一编码的数量（即唯一性规范性）
        count(*) as cnt
    from base
    group by date_format(ZSSJ, 'yyyyMMdd'), JGDM, SJTJRQ
)
insert into hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
select
    YWRQ,
    JGDM,
    'JKW-TJ-0002' as GZBH,
    sum(cnt) as JYJLSH,
    sum(case when SJZDCTJBM_cnt = 1 then cnt else 0 end) as FHGZSH,
    'v2,0' as BBH,
    current_timestamp() as XGSHJ
from grouped
group by ywrq, JGDM;

--JKW-TJ-0003 体检物理检查科室结论表-实际体检日期及时性
with base as (
    select
        t1.SJZDCTJBM,
        t1.JCSJ,
        t1.YLJGYQDM,
        t2.ZSSJ,
        a.fj_yljgdm as JGDM
    from hive_jktj_prod_source_dwd.T_EXAMINATION_RESULTS t1
    left join hive_wsjk_v12_prod_source_ods.T_AUDIT_FINAL_STATUS t2
        on t1.sjzwybm = t2.sjzwybm and t1.sjzdctjbm = t2.sjzdctjbm and t1.yljgyqdm = t2.yljgyqdm
    left join hive_wsjk_v12_prod_source_ods.t_dic_yljgdm_comparison a
        on t1.yljgyqdm = a.yljgyqdm
    where t2.ZSSJ >= cast(from_unixtime(unix_timestamp('${BTIME}','yyyyMMdd')) as timestamp)
    and t2.ZSSJ < cast(from_unixtime(unix_timestamp('${ETIME}','yyyyMMdd')) as timestamp)
    and t1.xgbz = '1' and t2.xgbz = '1'
),
grouped as (
    SELECT
        date_format(ZSSJ,'yyyyMMdd') as YWRQ,
        JGDM,
        count(1) AS JYJLSH,
        SUM(CASE WHEN timestamp(t3.sjtjrq) < b.jcsj then 1 else 0 end ) as FHGZSH
    from base b
    left join hive_jktj_prod_source_dwd.T_PERSONAL_RESERVATION t3
        on b.sjzdctjbm = t3.sjzdctjbm and b.yljgyqdm = t3.yljgyqdm
    where t3.xgbz = '1'
    group by date_format(ZSSJ,'yyyyMMdd'),JGDM
)
insert into hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    YWRQ,
    JGDM,
    'JKW-TJ-0003' as GZBH,
    JYJLSH,
    FHGZSH,
    'v2,0' as BBH,
    current_timestamp() as XGSHJ
from grouped;

--JKW-TJ-0004 体检物理检查科室结论表-体检科室与受检者科室一致性
--测量方法"检查时间，不早于体检日期
--体检物理检查结果明细表（T_EXAMINATION_RESULTS_DETAIL）与个人体检信息（T_PERSONAL_RESERVATION）
--通过医疗机构院区代码 YLJGYQDM 和 受检者单次体检编码关联，实际体检日期 SJTJRQ应早于检查时间JCSJ"

with base as (
    select
        t1.SJZDCTJBM,
        t1.JCSJ,
        t1.YLJGYQDM,
        t2.ZSSJ,
        a.fj_yljgdm as JGDM
    from hive_jktj_prod_source_dwd.T_EXAMINATION_RESULTS_DETAIL t1
    left join hive_jktj_prod_source_dwd.T_AUDIT_FINAL_STATUS t2
        on t1.sjzwybm = t2.sjzwybm and t1.sjzdctjbm = t2.sjzdctjbm and t1.yljgyqdm = t2.yljgyqdm
    left join hive_wsjk_v12_prod_source_ods.t_dic_yljgdm_comparison a
        on t1.yljgyqdm = a.yljgyqdm
    where t2.ZSSJ >= cast(from_unixtime(unix_timestamp('${BTIME}','yyyyMMdd')) as timestamp)
    and t2.ZSSJ < cast(from_unixtime(unix_timestamp('${ETIME}','yyyyMMdd')) as timestamp)
    and t1.xgbz = '1' and t2.xgbz = '1'
),
grouped as (
    select
        date_format(ZSSJ,'yyyyMMdd') as YWRQ,
        jgdm,
        count(1) as jyjlsh,
        sum(case when timestamp(t3.sjtjrq) < b.jcsj then 1 else 0 end) as fhgzsh
    from base B
    left join hive_jktj_prod_source_dwd.T_PERSONAL_RESERVATION t3
        on b.sjzdctjbm = t3.sjzdctjbm and b.yljgyqdm = t3.yljgyqdm
    where t3.xgbz = '1'
    group by date_format(ZSSJ,'yyyyMMdd'),JGDM
)
insert into hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
select
    YWRQ,
    JGDM,
    'JKW-TJ-0004' as GZBH,
    JYJLSH,
    FHGZSH,
    'v2,0' as BBH,
    current_timestamp() as XGSHJ
from grouped;

--JKW-TJ-0005 体检辅助检查放射、超声、内镜、病理、心电图、功能检查等）结果表-实际体检日期及时性






