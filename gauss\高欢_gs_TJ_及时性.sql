
--40035 主检报告记录上传及时性

INSERT INTO  prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
	,JGDM
	,'40035' as <PERSON><PERSON><PERSON><PERSON>
	,JYJLS<PERSON> as JY<PERSON><PERSON><PERSON>
	,case when FHGZSH>=0 then FHGZSH else 0.00 end as FHGZSH
	,'v2,0' as bbh
	,current_timestamp as XGSHJ
FROM (
	SELECT 
        t1.ZSSJ AS YWRQ,
		t2.FJ_YLJGYQDM AS JGDM,
		COUNT(1) AS JYJLSH,
		sum(round(date_part('epoch',t1.JLGXSJ) - date_part('epoch',t1.ZSSJ)) / 86400, 2) AS FHGZSH
	FROM DWD_tj.T_AUDIT_FINAL_STATUS t1
	LEFT JOIN prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 ON t1.YLJGYQDM = t2.yljgyqdm
	WHERE t1.XGBZ = '1'
	    --AND t1.bbh = 'v2,0'
		AND to_char(t1.ZSSJ,'YYYYMMDD') >= '${BTIME}'
		AND to_char(t1.ZSSJ,'YYYYMMDD') <  '${ETIME}'
	GROUP BY t1.ZSSJ,t2.FJ_YLJGYQDM
	);  
