INSERT INTO prefix_dq.dbo.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT CONVERT(VARCHAR(8), CONVERT(DATE, GETDATE() - 1), 112) YWRQ,
    JGDM,
    '30060' GZBH,
    CWZ,
ISNULL(CWL, 0) AS CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT JGDM,
            cwz,
            CWL,
            row_number() OVER (
                partition by jgdm
                ORDER BY CWL DESC
            ) rn
        FROM (
                SELECT t2.FJ_YLJGYQDM JGDM,
                    COUNT(1) CWL,
                    KH as CWZ
                FROM DWD.dbo.TB_YL_PATIENT_INFORMATION t1
                    LEFT JOIN (
                        SELECT yljgyqdm,
                            FJ_YLJGYQDM
                        FROM prefix_dq.dbo.T_DIC_YLJGDM_COMPARISON
                    ) t2 ON t1.YLJGYQDM = t2.yljgyqdm
                WHERE (
                        CASE
                            -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                            WHEN trim(t1.KLX) = '0'
                            AND PATINDEX('[^0-9A-Z]', t1.KH) = 0 
                            AND datalength(t1.KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                            WHEN trim(t1.KLX) = '1'
                            AND PATINDEX('[^0-9]', t1.KH) = 0
                            AND datalength(t1.KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                            WHEN trim(t1.KLX) = '2'
                            AND PATINDEX('[^0-9]', t1.KH) = 0
                            AND datalength(t1.KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                            WHEN trim(t1.KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                            WHEN trim(t1.KLX) = '9' THEN 1 -- 默认情况
                            ELSE 0
                        END
                    ) = 0
                    AND t1.XGBZ = '1'
                    AND LTRIM(RTRIM(t1.KLX)) IN (
                        '0',
                        '1',
                        '2',
                        '3',
                        '4',
                        '9'
                    ) --AND t1.bbh = 'v1.2'
                GROUP BY t2.FJ_YLJGYQDM,
                    KH
            ) a
    ) AS a2
WHERE rn <= 10;




INSERT INTO prefix_dq.dbo.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT CONVERT(VARCHAR(8), CONVERT(DATE, GETDATE() - 1), 112) YWRQ,
    JGDM,
    '30074' GZBH,
    CWZ,
(ISNULL(CWL, 0)) AS CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT JGDM,
            cwz,
            CWL,
            row_number() OVER (
                partition by jgdm
                ORDER BY CWL DESC
            ) rn
        FROM (
                SELECT t2.FJ_YLJGYQDM JGDM,
                    COUNT(1) CWL,
                    t1.KH CWZ
                FROM DWD.dbo.TB_RIS_PATIENT_INFORMATION t1
                    LEFT JOIN (
                        SELECT yljgyqdm,
                            FJ_YLJGYQDM
                        FROM prefix_dq.dbo.T_DIC_YLJGDM_COMPARISON
                    ) t2 ON t1.YLJGYQDM = t2.yljgyqdm
                WHERE (
                        CASE
                            -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                            WHEN trim(t1.KLX) = '0'
                            AND PATINDEX('[^0-9A-Z]', t1.KH) = 0 
                            AND datalength(t1.KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                            WHEN trim(t1.KLX) = '1'
                            AND PATINDEX('[^0-9]', t1.KH) = 0
                            AND datalength(t1.KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                            WHEN trim(t1.KLX) = '2'
                            AND PATINDEX('[^0-9]', t1.KH) = 0
                            AND datalength(t1.KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                            WHEN trim(t1.KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                            WHEN trim(t1.KLX) = '9' THEN 1 -- 默认情况
                            ELSE 0
                        END
                    ) = 0
                    AND t1.XGBZ = '1'
                    AND LTRIM(RTRIM(t1.KLX)) IN (
                        '0',
                        '1',
                        '2',
                        '3',
                        '4',
                        '9'
                    ) --AND t1.bbh = 'v1.2'
                GROUP BY t2.FJ_YLJGYQDM,
                    t1.KH
            ) a
    ) b
WHERE rn <= 10;