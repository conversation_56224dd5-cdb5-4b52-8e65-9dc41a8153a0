
--40035 主检报告记录上传及时性update

update t1
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40035'
from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
WHERE round(DATEDIFF(SECOND, t1.ZSSJ, t1.JLGXSJ) * 1.0 / 86400, 2) > 1
    and xgbz = '1'
    and CONVERT(VARCHAR(8),t1.<PERSON><PERSON><PERSON>,112) >= '${BTIME}'
    and CONVERT(VARCHAR(8),t1.ZSSJ,112) < '${ETIME}'
    and (t1.war_note not like '%40035%' or t1.war_note is null)
    and t1.send_flag='0';

