

-- 10211 基本信息-体检实验室检验报告表头受检者唯一编码关联率
with BASE as (
    select
		t4.YWRQ,
        t3.SJZWYBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.TB_TJ_LIS_REPORT t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),                                         
ERROR_DATA AS (
    SELECT YWRQ,
           JGDM,
           SJZWYBM AS cwz,
           COUNT(1) AS CWL,
           row_number() over(partition by YWRQ, JGDM order by COUNT(1) desc) rn
    FROM BASE b
    WHERE NOT EXISTS (
        SELECT 1
        FROM dwd_tj.dbo.T_PERSONAL_BASE_INFO t3
        LEFT JOIN prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c  --机构代码字典表
            ON t3.yljgyqdm = c.yljgyqdm
        WHERE b.SJZWYBM = t3.SJZWYBM
            AND datalength(b.SJZWYBM) = datalength(t3.SJZWYBM)
            AND t3.xgbz = '1'
            AND b.JGDM = c.FJ_YLJGYQDM
    )
    GROUP BY YWRQ, JGDM, SJZWYBM
)
insert INTO prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, BBH, xgshj)
SELECT YWRQ,
       JGDM,
       '10211' AS GZBH,
       cwz,
       ISNULL(CWL, 0) AS CWL,
       'v2.0' AS BBH,
       GETDATE() AS XGSHJ
FROM ERROR_DATA
WHERE rn <= 10;

-- 10212 基本信息-体检实验室结果指标表受检者唯一编码关联率
with BASE as (
    select
		t4.YWRQ,
        t3.SJZWYBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.TB_TJ_LIS_INDICATORS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        SJZWYBM as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_BASE_INFO t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZWYBM = t3.SJZWYBM
            and datalength(b.SJZWYBM) = datalength(t3.SJZWYBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, SJZWYBM
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10212' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;

-- 10213   基本信息-重要异常结果记录受检者唯一编码关联率

with BASE as (
    select
		t4.YWRQ,
        t3.SJZWYBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_POSITIVE_AUDIT t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        SJZWYBM as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_BASE_INFO t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZWYBM = t3.SJZWYBM
            and datalength(b.SJZWYBM) = datalength(t3.SJZWYBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, SJZWYBM
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10213' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;

-- 10214   基本信息-主检报告记录受检者唯一编码关联率

with BASE as (
    select
		CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
        t1.SJZWYBM,
        t2.FJ_YLJGYQDM as JGDM
    from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
    left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2
        on t1.yljgyqdm = t1.yljgyqdm
    where t1.xgbz = '1' 
		and CONVERT(VARCHAR(8), t1.ZSSJ, 112) >= '${BTIME}'
		and CONVERT(VARCHAR(8), t1.ZSSJ, 112) < '${ETIME}'
),
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        SJZWYBM as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_BASE_INFO t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZWYBM = t3.SJZWYBM
            and datalength(b.SJZWYBM) = datalength(t3.SJZWYBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, SJZWYBM
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10214' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;


-- 10215  体检信息-个人体检项目总表受检者单次体检编码关联率

with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_PERSONAL_RESERVATION_TOTAL t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        sjzdctjbm as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
            and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10215' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;


-- 10216 体检信息-体检健康问卷采集表受检者单次体检编码关联率

with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_EXAMINATION_QNA_INFO t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        sjzdctjbm as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
            and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10216' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;


-- 10217  体检信息-体检一般检查结果表受检者单次体检编码关联率

with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_EXAMINATION_BASIC_RESULTS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),    
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        SJZDCTJBM as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
            and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10217' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;

-- 10218 体检信息-体检物理检查科室结论表受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_EXAMINATION_RESULTS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
), 
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        sjzdctjbm as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
            and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10218' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;

--10219 体检信息-体检物理检查结果明细表受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_EXAMINATION_RESULTS_DETAIL t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        sjzdctjbm as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
            and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10219' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;
                                                    
-- 10220 体检信息-体检辅助检查放射、超声、内镜、病理、心电图、功能检查等）结果表受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_EXAMINATION_SUPPORTIVE_RESULTS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        sjzdctjbm as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
            and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10220' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;

-- 10221 体检信息-体检实验室检验报告表头受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.TB_TJ_LIS_REPORT t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        sjzdctjbm as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
            and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10221' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;

-- 10222  体检信息-体检实验室结果指标表受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.TB_TJ_LIS_INDICATORS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
), 
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        sjzdctjbm as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
            and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10222' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;


-- 10223 体检信息-重要异常结果记录受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_POSITIVE_AUDIT t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        sjzdctjbm as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
            and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10223' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;

--10224  体检信息-主检报告记录受检者单次体检编码关联率

with BASE as (
    select
		CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
        t1.SJZDCTJBM,
        t2.FJ_YLJGYQDM as JGDM
    from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
    left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2
        on t1.yljgyqdm = t1.yljgyqdm
    where t1.xgbz = '1' 
		and CONVERT(VARCHAR(8), t1.ZSSJ, 112) >= '${BTIME}'
		and CONVERT(VARCHAR(8), t1.ZSSJ, 112) < '${ETIME}'
),
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        sjzdctjbm as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
            and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10224' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;


-- 10225 体检信息-体检物理检查结果明细表项目流水号关联率

with BASE as (
    select
		t4.YWRQ,
		t3.XMLSH,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.XMLSH,
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_EXAMINATION_RESULTS_DETAIL t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        xmlsh as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION_TOTAL t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.XMLSH = t3.XMLSH
          and b.SJZDCTJBM = t3.SJZDCTJBM
          and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and datalength(b.XMLSH) = datalength(t3.XMLSH)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, xmlsh
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10225' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;


-- 10226 体检信息-体检辅助检查放射、超声、内镜、病理、心电图、功能检查等）结果表项目流水号关联率
with BASE as (
    select
		t4.YWRQ,
		t3.XMLSH,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.XMLSH,
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_EXAMINATION_SUPPORTIVE_RESULTS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        xmlsh as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION_TOTAL t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.XMLSH = t3.XMLSH
          and b.SJZDCTJBM = t3.SJZDCTJBM
          and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and datalength(b.XMLSH) = datalength(t3.XMLSH)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, xmlsh
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10226' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;


-- 10227 体检信息-体检物理检查结果明细表检验指标流水号关联率
with BASE as (
    select
		t4.YWRQ,
		t3.JYZBLSH,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.JYZBLSH,
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.TB_TJ_LIS_INDICATORS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        jyzblsh as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_PERSONAL_RESERVATION_TOTAL t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.JYZBLSH = t3.XMLSH
          and b.SJZDCTJBM = t3.SJZDCTJBM
          and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and datalength(b.JYZBLSH) = datalength(t3.JYZBLSH)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, jyzblsh
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10227' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;

-- 10228 物理检查-体检物理检查结果明细表受检者单次体检编码、受检者唯一编码、科室ID关联率
with BASE as (
    select
		t4.YWRQ,
		t3.KSID,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.KSID,
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_EXAMINATION_RESULTS_DETAIL t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        sjzdctjbm as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.T_EXAMINATION_RESULTS t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.KSID = t3.KSID
          and b.SJZDCTJBM = t3.SJZDCTJBM
          and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and datalength(b.KSID) = datalength(t3.KSID)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, sjzdctjbm
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10228' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;

-- 10229 检验报告-体检实验室结果指标表受检者单次体检编码、受检者唯一编码、报告单号关联率

with BASE as (
    select
		t4.YWRQ,
		t3.BGDH,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.BGDH,
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.TB_TJ_LIS_INDICATORS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				CONVERT(VARCHAR(8), t1.ZSSJ, 112) as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
ERROR_DATA as (
    select
        YWRQ,
        JGDM,
        bgdh as cwz,
        count(1) as cwl,
        row_number() over(partition by YWRQ, JGDM order by count(1) desc) rn
    from BASE b
    where not exists (
        select 1
        from dwd_tj.dbo.TB_TJ_LIS_REPORT t3
        left join prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.BGDH = t3.BGDH
          and b.SJZDCTJBM = t3.SJZDCTJBM
          and datalength(b.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and datalength(b.BGDH) = datalength(t3.BGDH)
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM, bgdh
)
insert into prefix_dq_tj.dbo.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
select
    YWRQ,
    JGDM,
    '10229' as GZBH,         -- 指标编号请按实际调整
    cwz,
    ISNULL(cwl, 0) as cwl,
    'v2.0' as BBH,         -- 版本号请按实际调整
    GETDATE() as XGSHJ
from ERROR_DATA
where rn <= 10;
