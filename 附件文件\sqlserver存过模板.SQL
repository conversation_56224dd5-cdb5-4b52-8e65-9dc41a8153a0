CREATE PROCEDURE P_count_XXXXXX
(
	@Resulst INT OUTPUT,
    @vBatch_Seq NVARCHAR(8)
)
AS


BEGIN
    SET NOCOUNT ON;
    
     -- 声明变量
    DECLARE @vB_TIME DATETIME = GETDATE();
    DECLARE @vE_TIME DATETIME;
    DECLARE @vOBJ_CNT INT = 0;
    DECLARE @vSUC_CNT INT = 0;
    DECLARE @vERR_CNT INT = 0;
    DECLARE @vRowSno INT;
    DECLARE @vPARA_IN NVARCHAR(128);
    DECLARE @vERR_ID NVARCHAR(500) = NULL;
    DECLARE @vjgdm VARCHAR(8);
    DECLARE @ywsj VARCHAR(8);
    DECLARE @vBatch_ID VARCHAR(36); 
    DECLARE @vYxbz VARCHAR(2) = 1;
--     DECLARE @Resulst INT = 0;
    SELECT DISTINCT @vjgdm = fj_yljgyqdm FROM prefix_his.dbo.t_dic_yljgdm_comparison;
    
    
    DECLARE @vTable NVARCHAR(128);
    DECLARE @vLog_Resulst INT;
    DECLARE @vPrc_Resulst INT = 0;
    DECLARE @vPrc_Name NVARCHAR(128) = 'count_XXXXXX ';
    DECLARE @vBATCH_NM NVARCHAR(128);
	
	
    SET @vBATCH_NM = @vPrc_Name;
	PRINT @vBATCH_NM;
    SET @vPARA_IN = @vBatch_Seq;
    SET @vLog_Resulst = 0;
    SET @vBatch_ID = NEWID(); 
    SET @vE_TIME = GETDATE();
    SET @vPrc_Resulst=3;
	
	    -- 插入明细日志
    SET @vLog_Resulst = 0;
    SET @vE_TIME = GETDATE();
    EXEC prefix_dq.dbo.P_SysIns_LogDetail @vPrc_Resulst, @vLog_Resulst OUTPUT, @vBatch_Seq, @vBatch_ID, @vBATCH_NM,@vPARA_IN, @vB_TIME, @vE_TIME,  @vOBJ_CNT, @vSUC_CNT, @vERR_CNT,@vERR_ID, @vYxbz;

    
     -- 声明游标变量
    DECLARE @Mycursor CURSOR;
    
    BEGIN TRY
     --  开始事务
    BEGIN TRANSACTION;

        
		-- 获取所有的业务时间

         --  声明并打开游标
        SET @Mycursor = CURSOR FOR
--         SELECT COL_NAME AS TAB_CODE FROM prefix_dq.dbo.F_SysGet_TabList();
        SELECT ywsj FROM prefix_dq.dbo.v_get_ywsj;
        OPEN @Mycursor;
        
         --  循环处理每个日期
        FETCH NEXT FROM @Mycursor INTO @ywsj;
        WHILE @@FETCH_STATUS = 0
            BEGIN
            SET @vBATCH_NM = @vPrc_Name + '[' + @ywsj + ']';
            SET @vB_TIME = GETDATE();
            SET @vPARA_IN = @ywsj;
            SET @vPrc_Resulst = 0;
			
			EXEC prefix_dq.dbo.P_Get_MaxRowSno @vBatch_Seq ,N'tb_uploads_stat', @vRowSno OUTPUT;
			
			
			代码块
			代码块
			代码块
			代码块
			代码块
			代码块
			代码块
			
			
            SET @vE_TIME = GETDATE();
            SET @Resulst = CASE WHEN @vPrc_Resulst > 0 THEN @Resulst ELSE @vPrc_Resulst END;
                
             -- 日志记录
            PRINT @vBATCH_NM;
            SET @vLog_Resulst = @vPrc_Resulst;
			SET @vBatch_ID = NEWID(); 
            EXEC prefix_dq.dbo.P_SysIns_LogDetail @vPrc_Resulst, @vLog_Resulst OUTPUT, @vBatch_Seq, @vBatch_ID, @vBATCH_NM,@vPARA_IN, @vB_TIME, @vE_TIME,  @vOBJ_CNT, @vSUC_CNT, @vERR_CNT,@vERR_ID, @vYxbz;

			-- 获取下一个表
            FETCH NEXT FROM @Mycursor INTO @ywsj;
        END
        
         -- 关闭并释放游标
        IF CURSOR_STATUS('variable', '@Mycursor') >= 0
        BEGIN
            CLOSE @Mycursor;
            DEALLOCATE @Mycursor;
        END;
		COMMIT TRANSACTION;
		PRINT 'Mycursor finish';
		SET @Resulst = 1;
	END TRY
	BEGIN CATCH
		-- 错误处理
		IF @@TRANCOUNT > 0
			ROLLBACK TRANSACTION;
			
		-- 关闭并释放游标（如果打开）
		IF CURSOR_STATUS('variable', '@Mycursor') >= 0
		BEGIN
			CLOSE @Mycursor;
			DEALLOCATE @Mycursor;
		END;
		
		-- 记录错误信息
		SET @vERR_ID = ERROR_MESSAGE();
		SET @Resulst = 0;
		SET @vPrc_Resulst = 0;
		SET @vBatch_ID = NEWID(); -- 使用SET赋值而非DECLARE
		
		--  日志记录
		SET @vBATCH_NM = @vPrc_Name + '[错误]';
		-- 记录错误日志
		SET @vLog_Resulst = @vPrc_Resulst;
		EXEC prefix_dq.dbo.P_SysIns_LogDetail @vPrc_Resulst, @vLog_Resulst OUTPUT, @vBatch_Seq, @vBatch_ID, @vBATCH_NM,@vPARA_IN, @vB_TIME, @vE_TIME,  @vOBJ_CNT, @vSUC_CNT, @vERR_CNT,@vERR_ID, @vYxbz;
	END CATCH;
END;




--8.4
--查询表数据量存过
CREATE PROCEDURE [dbo].[sp_GetTableRowCounts]
    @DatabaseName NVARCHAR(128) = NULL,    -- 数据库名参数，默认为空
    @SchemaName NVARCHAR(128) = NULL,      -- 架构名参数，默认为空  
    @TableName NVARCHAR(128) = NULL,       -- 表名参数，默认为空
    @IncludeSystemTables BIT = 0           -- 是否包含系统表，默认不包含
AS
BEGIN
    SET NOCOUNT ON;  -- 关闭行数统计信息，提高性能
    
    -- 如果未指定数据库名，使用当前数据库
    IF @DatabaseName IS NULL
        SET @DatabaseName = DB_NAME();  -- DB_NAME()获取当前数据库名
    DECLARE @SQL NVARCHAR(MAX);
    
    -- 构建动态SQL
    SET @SQL = N'
-- 主查询：从系统视图获取表信息
SELECT 
    ''' + @DatabaseName + ''' AS DatabaseName,           -- 数据库名
    SCHEMA_NAME(t.schema_id) AS SchemaName,              -- 架构名
    t.name AS TableName,                                 -- 表名
    p.rows AS RowCount,                                  -- 行数
    -- 计算总空间(MB) = 总页数 * 8KB / 1024
    CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS TotalSpaceMB,
    -- 计算已用空间(MB)
    CAST(ROUND(((SUM(a.used_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS UsedSpaceMB,
    -- 计算未用空间(MB) = 总页数 - 已用页数
    CAST(ROUND(((SUM(a.total_pages) - SUM(a.used_pages)) * 8) / 1024.00, 2) AS NUMERIC(36, 2)) AS UnusedSpaceMB
FROM ' + QUOTENAME(@DatabaseName) + '.sys.tables t              -- 用户表信息
INNER JOIN ' + QUOTENAME(@DatabaseName) + '.sys.indexes i       -- 索引信息
    ON t.object_id = i.object_id
INNER JOIN ' + QUOTENAME(@DatabaseName) + '.sys.partitions p    -- 分区信息(包含行数)
    ON i.object_id = p.object_id AND i.index_id = p.index_id
INNER JOIN ' + QUOTENAME(@DatabaseName) + '.sys.allocation_units a  -- 存储分配单元
    ON p.partition_id = a.container_id
LEFT OUTER JOIN ' + QUOTENAME(@DatabaseName) + '.sys.schemas s   -- 架构信息
    ON t.schema_id = s.schema_id
    WHERE 
        t.is_ms_shipped = ' + CASE WHEN @IncludeSystemTables = 1 THEN '0' ELSE '0' END + '
        AND i.object_id > 255';
    
    -- 添加架构过滤条件
    IF @SchemaName IS NOT NULL
        SET @SQL = @SQL + ' AND SCHEMA_NAME(t.schema_id) = ''' + @SchemaName + '''';
    
    -- 添加表名过滤条件
    IF @TableName IS NOT NULL
        SET @SQL = @SQL + ' AND t.name LIKE ''%' + @TableName + '%''';
    
    SET @SQL = @SQL + N'
    GROUP BY t.name, SCHEMA_NAME(t.schema_id), p.rows
    ORDER BY p.rows DESC, TotalSpaceMB DESC';
    
    -- 执行动态SQL
    EXEC sp_executesql @SQL;
    
    -- 输出汇总信息
    PRINT '数据库: ' + @DatabaseName + ' 表数据量统计完成';
    PRINT '执行时间: ' + CONVERT(VARCHAR(20), GETDATE(), 120);
END