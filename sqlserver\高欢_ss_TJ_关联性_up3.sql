;



-- 10211 基本信息-体检实验室检验报告表头受检者唯一编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10211'
from dwd_tj.dbo.TB_TJ_LIS_REPORT t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.dbo.T_PERSONAL_BASE_INFO t3
        where t1.SJZWYBM = t3.SJZWYBM and datalength(t1.SJZWYBM) = datalength(t3.SJZWYBM) and t1.posid = t3.posid and t3.xgbz = '1'
     )
    and CONVERT(VARCHAR(8), t2.<PERSON><PERSON><PERSON>, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.<PERSON><PERSON><PERSON>, 112) <'${ETIME}'
    and (t1.war_note not like '%10211%' or t1.war_note is null)
    and t1.send_flag='0';


-- 10212 基本信息-体检实验室结果指标表受检者唯一编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10212'
FROM dwd_tj.dbo.TB_TJ_LIS_INDICATORS t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_BASE_INFO t3
        where t1.SJZWYBM = t3.SJZWYBM and datalength(t1.SJZWYBM) = datalength(t3.SJZWYBM) and t1.posid = t3.posid and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10212%' or t1.war_note is null)
    and t1.send_flag='0';


-- 10213   基本信息-重要异常结果记录受检者唯一编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10213'
FROM dwd_tj.dbo.T_POSITIVE_AUDIT t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_BASE_INFO t3
        where t1.SJZWYBM = t3.SJZWYBM and datalength(t1.SJZWYBM) = datalength(t3.SJZWYBM) and t1.posid = t3.posid and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10213%' or t1.war_note is null)
    and t1.send_flag='0';

-- 10214   基本信息-主检报告记录受检者唯一编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10214'
FROM dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_BASE_INFO t3
        where t1.SJZWYBM = t3.SJZWYBM and datalength(t1.SJZWYBM) = datalength(t3.SJZWYBM) and t1.posid = t3.posid and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t1.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t1.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10214%' or t1.war_note is null)
    and t1.send_flag='0';

-- 10215  体检信息-个人体检项目总表受检者单次体检编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10215'
FROM dwd_tj.dbo.T_PERSONAL_RESERVATION_TOTAL t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10215%' or t1.war_note is null)
    and t1.send_flag='0';


-- 10216 体检信息-体检健康问卷采集表受检者单次体检编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10216'
FROM dwd_tj.dbo.T_EXAMINATION_QNA_INFO t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10216%' or t1.war_note is null)
    and t1.send_flag='0';

-- 10217  体检信息-体检一般检查结果表受检者单次体检编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10217'
FROM dwd_tj.dbo.T_EXAMINATION_BASIC_RESULTS t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10217%' or t1.war_note is null)
    and t1.send_flag='0';


-- 10218 体检信息-体检物理检查科室结论表受检者单次体检编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10218'
FROM dwd_tj.dbo.T_EXAMINATION_RESULTS t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10218%' or t1.war_note is null)
    and t1.send_flag='0';

--10219 体检信息-体检物理检查结果明细表受检者单次体检编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10219'
FROM dwd_tj.dbo.T_EXAMINATION_RESULTS_DETAIL t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10219%' or t1.war_note is null)
    and t1.send_flag='0';

-- 10220 体检信息-体检辅助检查放射、超声、内镜、病理、心电图、功能检查等）结果表受检者单次体检编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10220'
FROM dwd_tj.dbo.T_EXAMINATION_SUPPORTIVE_RESULTS t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10220%' or t1.war_note is null)
    and t1.send_flag='0';

-- 10221 体检信息-体检实验室检验报告表头受检者单次体检编码关联率
UPDATE t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10221'
FROM dwd_tj.dbo.TB_TJ_LIS_REPORT t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10221%' or t1.war_note is null)
    and t1.send_flag='0';


-- 10222  体检信息-体检实验室结果指标表受检者单次体检编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10222'
FROM dwd_tj.dbo.TB_TJ_LIS_INDICATORS t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10222%' or t1.war_note is null)
    and t1.send_flag='0';

-- 10223 体检信息-重要异常结果记录受检者单次体检编码关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10223'
FROM dwd_tj.dbo.T_POSITIVE_AUDIT t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10223%' or t1.war_note is null)
    and t1.send_flag='0';


--10224  体检信息-主检报告记录受检者单次体检编码关联率
UPDATE t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10224'
FROM dwd_tj.dbo.T_AUDIT_FINAL_STATUS t1
where  not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t1.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t1.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10224%' or t1.war_note is null)
    and t1.send_flag='0';

-- 10225 体检信息-体检物理检查结果明细表项目流水号关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10225'
FROM dwd_tj.dbo.T_EXAMINATION_RESULTS_DETAIL t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION_TOTAL t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and t1.XMLSH = t3.XMLSH 
        and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and datalength(t1.XMLSH) = datalength(t3.XMLSH)
        and t1.posid = t3.posid and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10225%' or t1.war_note is null)
    and t1.send_flag='0';


-- 10226 体检信息-体检辅助检查放射、超声、内镜、病理、心电图、功能检查等）结果表项目流水号关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10226'
FROM dwd_tj.dbo.T_EXAMINATION_SUPPORTIVE_RESULTS t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION_TOTAL t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and t1.XMLSH = t3.XMLSH 
        and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and datalength(t1.XMLSH) = datalength(t3.XMLSH)
        and t1.posid = t3.posid  and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10226%' or t1.war_note is null)
    and t1.send_flag='0';

-- 10227 体检信息-体检物理检查结果明细表检验指标流水号关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10227'
FROM dwd_tj.dbo.TB_TJ_LIS_INDICATORS t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_PERSONAL_RESERVATION_TOTAL t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and t1.JYZBLSH = t3.JYZBLSH 
        and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and datalength(t1.JYZBLSH) = datalength(t3.JYZBLSH)
        and t1.posid = t3.posid and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10227%' or t1.war_note is null)
    and t1.send_flag='0';

-- 10228 物理检查-体检物理检查结果明细表受检者单次体检编码、受检者唯一编码、科室ID关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10228'
FROM dwd_tj.dbo.T_EXAMINATION_RESULTS_DETAIL t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.T_EXAMINATION_RESULTS t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and t1.KSID = t3.KSID 
        and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and datalength(t1.KSID) = datalength(t3.KSID)
        and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10228%' or t1.war_note is null)
    and t1.send_flag='0';

-- 10229 检验报告-体检实验室结果指标表受检者单次体检编码、受检者唯一编码、报告单号关联率
UPDATE  t1
SET t1.war_flag = '1',
    t1.war_note = ISNULL(t1.war_note, '') + '&10229'
FROM dwd_tj.dbo.TB_TJ_LIS_INDICATORS t1
left join dwd_tj.dbo.T_AUDIT_FINAL_STATU t2 
    on t1.SJZDCTJBM = t2.SJZDCTJBM and t1.SJZWYBM = t2.SJZWYBM and t1.posid = t2.posid
where not exists (
        select 1 
        from dwd_tj.TB_TJ_LIS_REPORT t3
        where t1.SJZDCTJBM = t3.SJZDCTJBM and t1.BGDH = t3.BGDH 
        and datalength(t1.SJZDCTJBM) = datalength(t3.SJZDCTJBM) and datalength(t1.BGDH) = datalength(t3.BGDH)
        and t1.posid = t3.posid and t3.xgbz = '1'
     )
     and CONVERT(VARCHAR(8), t2.ZSSJ, 112) >='${BTIME}'  and CONVERT(VARCHAR(8), t2.ZSSJ, 112) <'${ETIME}'
    and (t1.war_note not like '%10229%' or t1.war_note is null)
    and t1.send_flag='0';




