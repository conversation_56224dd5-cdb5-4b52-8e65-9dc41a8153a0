
-- 10211 基本信息-体检实验室检验报告表头受检者唯一编码关联率

with BASE as (
    select
		t4.YWRQ,
        t3.SJZWYBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				t2.fj_yljgyqdm
			from dwd_tj.TB_TJ_LIS_REPORT t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),                                                  --关联主检记录报告的ZSSJ，关联机构代码字典表，筛选出在业务时间内的行为数据
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM       
),                                                    --统计在业务时间内的所有行为数据
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_BASE_INFO t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZWYBM = t3.SJZWYBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)                                                    --统计在业务时间内的所有符合规则的行为数据       
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL
select
    A.YWRQ as YWRQ,
    A.JGDM as JGDM,
    '10211' as GZBH,         
    A.JYJLSH as JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH, 
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;                                  

-- 10212 基本信息-体检实验室结果指标表受检者唯一编码关联率
with BASE as (
    select
		t4.YWRQ,
        t3.SJZWYBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				t2.fj_yljgyqdm
			from dwd_tj.TB_TJ_LIS_INDICATORS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM       
),                                                    --统计在业务时间内的所有行为数据
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_BASE_INFO t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZWYBM = t3.SJZWYBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)                                                                               
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ as YWRQ,
    A.JGDM as JGDM,
    '10212' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH as JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;

-- 10213   基本信息-重要异常结果记录受检者唯一编码关联率
with BASE as (
    select
		t4.YWRQ,
        t3.SJZWYBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_POSITIVE_AUDIT t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZWYBM,
                t1.SJZDCTJBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_BASE_INFO t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZWYBM = t3.SJZWYBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ as YWRQ,
    A.JGDM AS JGDM,
    '10213' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;

-- 10214   基本信息-主检报告记录受检者唯一编码关联率

with BASE as (
    select
		TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
        t1.SJZWYBM,
        t2.FJ_YLJGYQDM as JGDM
    from dwd_tj.T_AUDIT_FINAL_STATUS t1
    left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2
        on t1.yljgyqdm = t2.yljgyqdm
    where t1.xgbz = '1' 
		and TO_CHAR(t1.ZSSJ, 'YYYYMMDD') >= '${BTIME}'
		and TO_CHAR(t1.ZSSJ, 'YYYYMMDD') < '${ETIME}'
),
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_BASE_INFO t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZWYBM = t3.SJZWYBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10214' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;


-- 10215  体检信息-个人体检项目总表受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_PERSONAL_RESERVATION_TOTAL t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10215' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;


-- 10216 体检信息-体检健康问卷采集表受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_EXAMINATION_QNA_INFO t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10216' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;


-- 10217  体检信息-体检一般检查结果表受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_EXAMINATION_BASIC_RESULTS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10217' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;

-- 10218 体检信息-体检物理检查科室结论表受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_EXAMINATION_RESULTS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ,JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10218' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;

--10219 体检信息-体检物理检查结果明细表受检者单次体检编码关联率

with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_EXAMINATION_RESULTS_DETAIL t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10219' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;
                                                    
-- 10220 体检信息-体检辅助检查放射、超声、内镜、病理、心电图、功能检查等）结果表受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_EXAMINATION_SUPPORTIVE_RESULTS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10220' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;
-- 10221 体检信息-体检实验室检验报告表头受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.TB_TJ_LIS_REPORT t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10221' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;

-- 10222  体检信息-体检实验室结果指标表受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.TB_TJ_LIS_INDICATORS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),   
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10222' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;


-- 10223 体检信息-重要异常结果记录受检者单次体检编码关联率
with BASE as (
    select
		t4.YWRQ,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_POSITIVE_AUDIT t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM    
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10223' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;

--10224  体检信息-主检报告记录受检者单次体检编码关联率
with BASE as (
    select
		TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
        t1.SJZDCTJBM,
        t2.FJ_YLJGYQDM as JGDM
    from dwd_tj.T_AUDIT_FINAL_STATUS t1
    left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2
        on t1.yljgyqdm = t2.yljgyqdm
    where t1.xgbz = '1' 
		and TO_CHAR(t1.ZSSJ, 'YYYYMMDD') >= '${BTIME}'
		and TO_CHAR(t1.ZSSJ, 'YYYYMMDD') < '${ETIME}'
),
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10224' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;


-- 10225 体检信息-体检物理检查结果明细表项目流水号关联率
with BASE as (
    select
		t4.YWRQ,
		t3.XMLSH,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.XMLSH,
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_EXAMINATION_RESULTS_DETAIL t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION_TOTAL t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.XMLSH = t3.XMLSH
          and b.SJZDCTJBM = t3.SJZDCTJBM    
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10225' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;


-- 10226 体检信息-体检辅助检查放射、超声、内镜、病理、心电图、功能检查等）结果表项目流水号关联率
with BASE as (
    select
		t4.YWRQ,
		t3.XMLSH,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.XMLSH,
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_EXAMINATION_SUPPORTIVE_RESULTS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
A as (
    select
        YWRQ,
        JGDM,   
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION_TOTAL t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.XMLSH = t3.XMLSH
          and b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10226' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;


-- 10227 体检信息-体检实验室结果指标表检验指标流水号关联率
with BASE as (
    select
		t4.YWRQ,
		t3.JYZBLSH,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.JYZBLSH,
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.TB_TJ_LIS_INDICATORS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_PERSONAL_RESERVATION_TOTAL t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.JYZBLSH = t3.XMLSH
          and b.SJZDCTJBM = t3.SJZDCTJBM
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10227' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;

-- 10228 物理检查-体检物理检查结果明细表受检者单次体检编码、受检者唯一编码、科室ID关联率
with BASE as (
    select
		t4.YWRQ,
		t3.KSID,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.KSID,
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.T_EXAMINATION_RESULTS_DETAIL t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.T_EXAMINATION_RESULTS t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.KSID = t3.KSID
          and b.SJZDCTJBM = t3.SJZDCTJBM    
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10228' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;

-- 10229 检验报告-体检实验室结果指标表受检者单次体检编码、受检者唯一编码、报告单号关联率
with BASE as (
    select
		t4.YWRQ,
		t3.BGDH,
		t3.SJZDCTJBM,
        t3.FJ_YLJGYQDM as JGDM
    from (select  
				t1.BGDH,
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				t2.fj_yljgyqdm
			from dwd_tj.TB_TJ_LIS_INDICATORS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t3
	left join (select	
				t1.SJZDCTJBM,
                t1.SJZWYBM,
				TO_CHAR(t1.ZSSJ, 'YYYYMMDD') as YWRQ,
				t2.fj_yljgyqdm
			from dwd_tj.T_AUDIT_FINAL_STATUS t1
			left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
			where t1.xgbz = '1'
			) t4
	on t3.SJZWYBM = t4.SJZWYBM and t3.SJZDCTJBM = t4.SJZDCTJBM and t3.FJ_YLJGYQDM = t4.FJ_YLJGYQDM
	where t4.YWRQ >='${BTIME}' and t4.YWRQ < '${ETIME}'
),  
A as (
    select
        YWRQ,
        JGDM,
        count(1) as JYJLSH
    from BASE
    group by YWRQ, JGDM
),
B as (
    select
        YWRQ,
        JGDM,
        count(1) as FHGZSH
    from BASE b
    where exists (
        select 1
        from dwd_tj.TB_TJ_LIS_REPORT t3
        left join prefix_dq_tj.T_DIC_YLJGDM_COMPARISON c
            on t3.yljgyqdm = c.yljgyqdm
        where b.BGDH = t3.BGDH
          and b.SJZDCTJBM = t3.SJZDCTJBM    
          and t3.xgbz = '1'
          and b.JGDM = c.FJ_YLJGYQDM
    )
    group by YWRQ, JGDM
)
insert into prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, bbh, xgshj)
select
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '10229' as GZBH,         -- 指标编号请按实际调整
    A.JYJLSH AS JYJLSH,
    coalesce(B.FHGZSH, 0) as FHGZSH,
    'v2.0' as BBH,         -- 版本号请按实际调整
    current_timestamp as XGSHJ
from A
left join B on A.YWRQ = B.YWRQ and A.JGDM = B.JGDM
;
