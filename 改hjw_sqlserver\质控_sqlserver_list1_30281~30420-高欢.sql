INSERT INTO prefix_dq.dbo.TB_DQ_INDICATOR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        jyjlsh,
        fhgzsh,
        BBH,
        xgshj
    )
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30287' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'MZZD_ZY' AS ZDM,
    a.JYJLSH,
    ISNULL(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    current_timestamp AS XGSHJ
FROM (
        SELECT CONVERT(VARCHAR(8), b.GDR<PERSON>, 112) AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN PATINDEX('%[0-9]', b.MZZD_ZY) > 0
                    OR PATINDEX('%[0-9].[0-9]', b.<PERSON>ZZD_ZY) > 0
                    OR PATINDEX('%[0-9].[0-9][0-9]', b.<PERSON><PERSON><PERSON>_<PERSON>) > 0
                    OR PATINDEX('%[0-9].[0-9][0-9][0-9]', b.MZZD_ZY) > 0 THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.dbo.TB_BA_SYJBK b
            LEFT JOIN prefix_dq.dbo.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND CONVERT(VARCHAR(8), b.GDRQ, 112) >= '${BTIME}'
            AND CONVERT(VARCHAR(8), b.GDRQ, 112) < '${ETIME}' --AND b.bbh = 'v1.2'
            AND b.MZZD_ZY IS NOT NULL
            AND b.MZZD_ZY != ''
        GROUP BY d.FJ_YLJGYQDM,
            CONVERT(VARCHAR(8), b.GDRQ, 112)
    ) a;
----insert INTO tb_dq_rgfx_ywsj_m



INSERT INTO prefix_dq.dbo.TB_DQ_INDICATOR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        jyjlsh,
        fhgzsh,
        BBH,
        xgshj
    )
SELECT YWRQ,
    JGDM,
    '30352' GZBH,
    JYJLSH,
    ISNULL(FHGZSH, 0) FHGZSH,
    'v1.2' bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT t1.BGRQ YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COUNT(*) JYJLSH,
            SUM(
                CASE
                    -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                    WHEN trim(t1.KLX) = '0'
                    AND PATINDEX('[^0-9A-Z]', t1.KH) = 0 
                    AND datalength(t1.KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                    WHEN trim(t1.KLX) = '1'
                    AND PATINDEX('[^0-9]', t1.KH) = 0
                    AND datalength(t1.KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                    WHEN trim(t1.KLX) = '2'
                    AND PATINDEX('[^0-9]', t1.KH) = 0
                    AND datalength(t1.KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                    WHEN trim(t1.KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                    WHEN trim(t1.KLX) = '9' THEN 1 -- 默认情况
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.dbo.TB_GENETIC_REPORT t1
            LEFT JOIN (
                SELECT yljgyqdm,
                    FJ_YLJGYQDM
                FROM prefix_dq.dbo.T_DIC_YLJGDM_COMPARISON
            ) t2 ON t1.YLJGYQDM = t2.yljgyqdm
        WHERE TRIM(t1.KLX) IN (
                '0',
                '1',
                '2',
                '3',
                '4',
                '9'
            )
            AND t1.XGBZ = '1' --AND t1.bbh = 'v1.2'
            AND t1.BGRQ >= '${BTIME}'
            AND t1.BGRQ < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            t1.BGRQ
    ) A;



INSERT INTO prefix_dq.dbo.TB_DQ_INDICATOR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        jyjlsh,
        fhgzsh,
        BBH,
        xgshj
    )
SELECT YWRQ,
    JGDM,
    '30405' GZBH,
    JYJLSH,
    ISNULL(FHGZSH, 0) FHGZSH,
    'v1.2' bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT CONVERT(VARCHAR(8), t1.SHSJ, 112) YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COUNT(*) JYJLSH,
            SUM(
                CASE
                    -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                    WHEN trim(t1.KLX) = '0'
                    AND PATINDEX('[^0-9A-Z]', t1.KH) = 0 
                    AND datalength(t1.KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                    WHEN trim(t1.KLX) = '1'
                    AND PATINDEX('[^0-9]', t1.KH) = 0
                    AND datalength(t1.KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                    WHEN trim(t1.KLX) = '2'
                    AND PATINDEX('[^0-9]', t1.KH) = 0
                    AND datalength(t1.KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                    WHEN trim(t1.KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                    WHEN trim(t1.KLX) = '9' THEN 1 -- 默认情况
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.dbo.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN (
                SELECT yljgyqdm,
                    FJ_YLJGYQDM
                FROM prefix_dq.dbo.T_DIC_YLJGDM_COMPARISON
            ) t2 ON t1.YLJGYQDM = t2.yljgyqdm
        WHERE TRIM(t1.KLX) IN (
                '0',
                '1',
                '2',
                '3',
                '4',
                '9'
            )
            AND t1.XGBZ = '1' --AND t1.bbh = 'v1.2'
            AND CONVERT(VARCHAR(8), t1.SHSJ, 112) >= '${BTIME}'
            AND CONVERT(VARCHAR(8), t1.SHSJ, 112) < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            CONVERT(VARCHAR(8), t1.SHSJ, 112)
    ) A;