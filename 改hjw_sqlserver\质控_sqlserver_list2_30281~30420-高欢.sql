INSERT INTO prefix_dq.dbo.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30287' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'MZZD_ZY' AS ZDM,
    CWZ,
    ISNULL(CWL, 0) AS CWL,
    'v1.2' bbh,
    current_timestamp AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                ORDER BY CWL DESC
            ) rn
        FROM (
                SELECT CONVERT(VARCHAR(8), b.GDR<PERSON>, 112) AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.MZZD_ZY AS CWZ
                FROM DWD.dbo.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.dbo.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND CONVERT(VARCHAR(8), b.GDRQ, 112) >= '${BTIME}'
                    AND CONVERT(VARCHAR(8), b.GDRQ, 112) < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN PATINDEX('%[0-9]', b.MZZD_ZY) > 0
                            OR PATINDEX('%[0-9].[0-9]', b.MZZD_ZY) > 0
                            OR PATINDEX('%[0-9].[0-9][0-9]', b.MZZD_ZY) > 0
                            OR PATINDEX('%[0-9].[0-9][0-9][0-9]', b.MZZD_ZY) > 0 THEN 1
                            ELSE 0
                        END
                    ) = 0
                    AND b.MZZD_ZY IS NOT NULL
                    AND b.MZZD_ZY != ''
                GROUP BY d.FJ_YLJGYQDM,
                    CONVERT(VARCHAR(8), b.GDRQ, 112),
                    b.MZZD_ZY
            ) a ----insert INTO tb_dq_rgfx_ywsj_m
    ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.dbo.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ as YWRQ,
    JGDM as JGDM,
    '30352' GZBH,
    --'TB_GENETIC_REPORT' BM ,
    --'KH' ZDM,
    CWZ,
    ISNULL((CWL), 0) as CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                ORDER BY CWL DESC
            ) rn
        FROM (
                SELECT b.BGRQ YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    ISNULL(COUNT(1), 0) CWL,
                    b.KH AS CWZ
                FROM DWD.dbo.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.dbo.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE (
                        CASE
                            -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                            WHEN trim(b.KLX) = '0'
                            AND PATINDEX('[^0-9A-Z]', b.KH) = 0 
                            AND datalength(b.KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                            WHEN trim(b.KLX) = '1'
                            AND PATINDEX('[^0-9]', b.KH) = 0
                            AND datalength(b.KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                            WHEN trim(b.KLX) = '2'
                            AND PATINDEX('[^0-9]', b.KH) = 0
                            AND datalength(b.KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                            WHEN trim(b.KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                            WHEN trim(b.KLX) = '9' THEN 1 -- 默认情况
                            ELSE 0
                        END
                    ) = 0
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND b.BGRQ < '${ETIME}'
                    AND b.BGRQ >= '${BTIME}' --AND b.bbh = 'v1.2'
                    AND TRIM(b.KLX) IN (
                        '0',
                        '1',
                        '2',
                        '3',
                        '4',
                        '9'
                    )
                GROUP BY d.FJ_YLJGYQDM,
                    b.BGRQ,
                    b.KH
            ) a
    ) b
WHERE rn <= 10;

INSERT INTO prefix_dq.dbo.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ as YWRQ,
    JGDM AS JGDM,
    '30405' AS GZBH,
    CWZ AS CWZ,
    ISNULL(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                ORDER BY CWL DESC
            ) rn
        FROM (
                SELECT CONVERT(VARCHAR(8), t1.SHSJ, 112) YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    ISNULL(COUNT(1), 0) CWL,
                    KH CWZ
                FROM DWD.dbo.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.dbo.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        CASE
                    -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                    WHEN trim(t1.KLX) = '0'
                    AND PATINDEX('[^0-9A-Z]', t1.KH) = 0 
                    AND datalength(t1.KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                    WHEN trim(t1.KLX) = '1'
                    AND PATINDEX('[^0-9]', t1.KH) = 0
                    AND datalength(t1.KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                    WHEN trim(t1.KLX) = '2'
                    AND PATINDEX('[^0-9]', t1.KH) = 0
                    AND datalength(t1.KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                    WHEN trim(t1.KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                    WHEN trim(t1.KLX) = '9' THEN 1 -- 默认情况
                    ELSE 0
                        END
                    ) = 0
                    AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    AND TRIM(t1.KLX) IN (
                        '0',
                        '1',
                        '2',
                        '3',
                        '4',
                        '9'
                    )
                    AND CONVERT(VARCHAR(8), t1.SHSJ, 112) >= '${BTIME}'
                    AND CONVERT(VARCHAR(8), t1.SHSJ, 112) < '${ETIME}'
                GROUP BY t2.FJ_YLJGYQDM,
                    CONVERT(VARCHAR(8), t1.SHSJ, 112),
                    KH
            ) a
    ) b
WHERE rn <= 10;