
--1.机构分数排名  原版
select  shjqj,
    	ywrq,
    	fj_yljgmc,
    	ssqxmc,
    	AVG_JGPF,
		examined,
		rn
from (
    select 
    	shjqj,
    	ywrq,
    	fj_yljgmc,
    	ssqxmc,
    	AVG_JGPF,
		examined,
    	row_number () over (partition by shjqj,ywrq order by AVG_JGPF DESC) as rn
    from (
    	select 	
    		jg.shjqj,
    		jg.ywrq,
    		zd.fj_yljgmc,
    		zd.ssqxmc,
			zd.examined,
    		nvl(avg(jg.jgpf),0) as AVG_JGPF
    	from cendq_tj.TB_DQ_KHBG_JG jg
    	left join cendq.T_DEF_YLJGDM_COMPARISON zd
    	    on jg.JGDM = zd.FJ_YLJGYQDM
    	where shjqj = :shjqj
    	    and ywrq = :ywrq
		    and zd.examined in ('0','1')
    	 <#if qxdm?? && qxdm != ""> AND zd.QXDM=:qxdm</#if>
    	group by jg.shjqj,
    			 jg.ywrq,
    			 zd.fj_yljgmc,
    			 zd.ssqxmc,
				 zd.examined
    ) t1
) t2
where t2.rn<=8;

--机构分数排名  优化版
with  base_data as (
    select
        jg.shjqj,
        jg.ywrq,
        zd.fj_yljgmc,
        zd.ssqxmc,
        zd.examined,
        COALESCE(avg(jg.jgpf),0) as AVG_JGPF
    from cendq_tj.TB_DQ_KHBG_JG jg
    left join cendq.T_DEF_YLJGDM_COMPARISON zd
        on jg.jgdm = zd.FJ_YLJGYQDM 
    where zd.examined in ('0','1') 
        <#if qxdm?? && qxdm != ""> AND zd.QXDM=:qxdm</#if>    
        and shjqj = :shjqj and ywrq = :ywrq
    group by jg.shjqj,jg.ywrq,zd.fj_yljgmc,zd.ssqxmc,zd.examined
),
jgpf_ranked as (
    select
        shjqj,
        ywrq,
        fj_yljgmc,
        ssqxmc,
        avg_jgpf,
        examined,
        row_number () over (partition by shjqj,ywrq order by avg_jgpf desc) as rn
    from base_data
)
select * from jgpf_ranked where rn<=8  order by shjqj,ywrq,rn;


--2.新增数据量&历史累计数据量 原版
with lsljl as
(select sum(sjl.JYJLSH) lsljl
from cendq_tj.TB_DQ_AMOUNTOFDATA_YWSJ sjl
left join cendq.T_DEF_YLJGDM_COMPARISON zd
on sjl.JGDM = zd.fj_yljgyqdm
where 1=1
<#if qxdm?? && qxdm != ""> and zd.qxdm = :qxdm</#if>
<#if jgdm?? && jgdm != ""> and zd.fj_yljgyqdm = :jgdm</#if>),
xzsjl as
(select sum(sjl.JYJLSH) xzsjl
from cendq_tj.TB_DQ_AMOUNTOFDATA_YWSJ sjl
left join cendq.T_DEF_YLJGDM_COMPARISON zd
on sjl.JGDM = zd.fj_yljgyqdm
where 1=1
and ywrq between :pre_ywrq and :ywrq
<#if qxdm?? && qxdm != ""> and zd.qxdm = :qxdm</#if>
<#if jgdm?? && jgdm != ""> and zd.fj_yljgyqdm = :jgdm</#if>)
select 	 nvl(lsljl.lsljl,0) lsljl
		,nvl(xzsjl.xzsjl,0) xzsjl
from lsljl
cross join xzsjl;


--新增数据量&历史累计数据量 优化版1
with lsljl as(
    select 
        sum(sjl.JYJLSH) as sum_lsljl
    from cendq_tj.TB_DQ_AMOUNTOFDATA_YWSJ sjl
    left join cendq.T_DEF_YLJGDM_COMPARISON zd
        on sjl.JGDM = zd.fj_yljgyqdm
    where 1=1
        <#if qxdm?? && qxdm != ""> and zd.qxdm = :qxdm</#if>
        <#if jgdm?? && jgdm != ""> and zd.fj_yljgyqdm = :jgdm</#if>
),
xzsjl as (
    select 
        sum(sjl.JYJLSH) as sum_xzsjl
    from cendq_tj.TB_DQ_AMOUNTOFDATA_YWSJ sjl
    left join cendq.T_DEF_YLJGDM_COMPARISON zd
        on sjl.JGDM = zd.fj_yljgyqdm
    where 1=1
        and ywrq between :pre_ywrq and :ywrq
        <#if qxdm?? && qxdm != ""> and zd.qxdm = :qxdm</#if>
        <#if jgdm?? && jgdm != ""> and zd.fj_yljgyqdm = :jgdm</#if>)
select 	 
    COALESCE(lsljl.sum_lsljl,0) lsljl,
    COALESCE(xzsjl.sum_xzsjl,0) xzsjl
from lsljl
cross join xzsjl;

--新增数据量&历史累计数据量 优化版2
select
    sum(case when 1=1 then sjl.jyjlsh else 0 end) as lsljl,
    sum(case when ywrq between :pre_ywrq and :ywrq then sjl.jyjlsh else 0 end) as xzsjl
from cendq_tj.TB_DQ_AMOUNTOFDATA_YWSJ sjl
left join cendq.T_DEF_YLJGDM_COMPARISON zd
    on sjl.JGDM = zd.fj_yljgyqdm
where 1=1
    <#if qxdm?? && qxdm != ""> and zd.qxdm = :qxdm</#if>
    <#if jgdm?? && jgdm != ""> and zd.fj_yljgyqdm = :jgdm</#if>;


--3.体检-市区级得分 原版
with t1 as(
select 	jg.shjqj,
		jg.ywrq,
		<#if qxdm?? && qxdm != ""> zd.qxdm,</#if>
		avg(jg.LXXDF) LXXDF1,
		avg(jg.HFXDF) HFXDF1,
		avg(jg.JSXDF) JSXDF1,
		avg(jg.ZDFGL) ZDFGL1,
		avg(jg.ZDWZXDF) ZDWZXDF1,
		avg(jg.BDFGL) BDFGL1,
		avg(jg.BDWZXDF) BDWZXDF1,
		avg(jg.YJZKZQL) YJZKZQL1,
		avg(jg.YJZKZQXDF) YJZKZQXDF1,
		avg(jg.NHZKZQL) NHZKZQL1,
		avg(jg.NHZKZQXDF) NHZKZQXDF1,
		avg(jg.GLL) GLL1,
		avg(jg.GLXDF) GLXDF1,
		avg(jg.JGPF) JGPF1,
		avg(jg.SJ_ZF) JGPF_FZ1
from cendq_tj.TB_DQ_KHBG_JG jg
left join cendq.T_DEF_YLJGDM_COMPARISON zd
on jg.JGDM = zd.FJ_YLJGYQDM  
where zd.examined = '1' <#if qxdm?? && qxdm != ""> AND zd.QXDM=:qxdm</#if>
and	shjqj = :shjqj
and ywrq = :ywrq
group by shjqj,ywrq    <#if qxdm?? && qxdm != ""> ,zd.qxdm</#if>
),
t2 as (
select 	jg.shjqj,
		jg.ywrq,
		<#if qxdm?? && qxdm != ""> zd.qxdm,</#if>
		avg(jg.LXXDF) LXXDF2,
		avg(jg.HFXDF) HFXDF2,
		avg(jg.JSXDF) JSXDF2,
		avg(jg.ZDFGL) ZDFGL2,
		avg(jg.ZDWZXDF) ZDWZXDF2,
		avg(jg.BDFGL) BDFGL2,
		avg(jg.BDWZXDF) BDWZXDF2,
		avg(jg.YJZKZQL) YJZKZQL2,
		avg(jg.YJZKZQXDF) YJZKZQXDF2,
		avg(jg.NHZKZQL) NHZKZQL2,
		avg(jg.NHZKZQXDF) NHZKZQXDF2,
		avg(jg.GLL) GLL2,
		avg(jg.GLXDF) GLXDF2,
		avg(jg.JGPF) JGPF2,
		avg(jg.SJ_ZF) JGPF_FZ2
from cendq_tj.TB_DQ_KHBG_JG jg
left join cendq.T_DEF_YLJGDM_COMPARISON zd
on jg.JGDM = zd.FJ_YLJGYQDM 
where zd.examined = '1'   <#if qxdm?? && qxdm != ""> AND zd.QXDM=:qxdm</#if>
and	shjqj = :shjqj
and ywrq = :pre_ywrq
group by shjqj,ywrq    <#if qxdm?? && qxdm != ""> ,zd.qxdm</#if>
)
select 	t1.shjqj,
		t1.ywrq,
		<#if qxdm?? && qxdm != ""> t1.qxdm,</#if>
		round(t1.LXXDF1,2) LXXDF1,
		round(t1.HFXDF1,2) HFXDF1,
		round(t1.JSXDF1,2) JSXDF1,
		round(t1.ZDFGL1,2) ZDFGL1,
		round(t1.ZDWZXDF1,2) ZDWZXDF1,
		round(t1.BDFGL1,2) BDFGL1,
		round(t1.BDWZXDF1,2) BDWZXDF1,
		round(t1.YJZKZQL1,2) YJZKZQL1,
		round(t1.YJZKZQXDF1,2) YJZKZQXDF1,
		round(t1.NHZKZQL1,2) NHZKZQL1,
		round(t1.NHZKZQXDF1,2) NHZKZQXDF1,
		round(t1.GLL1,2) GLL1,
		round(t1.GLXDF1,2) GLXDF1,
		round(t1.JGPF1,2) JGPF1,
		round(t1.JGPF_FZ1,2) JGPF_FZ1,
		round(t2.LXXDF2,2) LXXDF2,
		round(t2.HFXDF2,2) HFXDF2,
		round(t2.JSXDF2,2) JSXDF2,
		round(t2.ZDFGL2,2) ZDFGL2,
		round(t2.ZDWZXDF2,2) ZDWZXDF2,
		round(t2.BDFGL2,2) BDFGL2,
		round(t2.BDWZXDF2,2) BDWZXDF2,
		round(t2.YJZKZQL2,2) YJZKZQL2,
		round(t2.YJZKZQXDF2,2) YJZKZQXDF2,
		round(t2.NHZKZQL2,2) NHZKZQL2,
		round(t2.NHZKZQXDF2,2) NHZKZQXDF2,
		round(t2.GLL2,2) GLL2, 
		round(t2.GLXDF2,2) GLXDF2,
		'0.00' zxzkdf1,
		'0.00' zxzkdf2,
		round((t1.JGPF1-t2.JGPF2)/t2.JGPF2*100.0,2) as dfhb
		from t1
		left join t2 on t1.shjqj= t2.shjqj;

--展示信息 原版
select bbh,jlgxsj
from cendq_tj.T_DEF_SYSTEM_INFORMATION
where bbh = :bbh

--参与机构数量（文字） 原版
with t1 as (
    select count(1) as sqyy
    from cendq.T_DEF_YLJGDM_COMPARISON 
    where levels = '1'
    and examined = '1'
    <#if qxdm?? && qxdm != ""> AND QXDM=:qxdm</#if>
),
t2 as (
    select count(1) as sqzl
    from cendq.T_DEF_YLJGDM_COMPARISON 
    where levels = '1'
    <#if qxdm?? && qxdm != ""> AND QXDM=:qxdm</#if>
),
t3 as (
    select count(1) as qjyy
    from cendq.T_DEF_YLJGDM_COMPARISON 
    where levels = '2'
        and examined = '1'
        <#if qxdm?? && qxdm != ""> AND QXDM=:qxdm</#if>),
t4 as (
    select count(1) as qjzl
    from cendq.T_DEF_YLJGDM_COMPARISON 
    where levels = '2'
        <#if qxdm?? && qxdm != ""> AND QXDM=:qxdm</#if>),
t5 as (
    select count(1) as sjyy
    from cendq.T_DEF_YLJGDM_COMPARISON 
    where levels = '3'
        and examined = '1'
        <#if qxdm?? && qxdm != ""> AND QXDM=:qxdm</#if>),
t6 as (
    select count(1) as sjzl
    from cendq.T_DEF_YLJGDM_COMPARISON 
    where levels = '3'
        <#if qxdm?? && qxdm != ""> AND QXDM=:qxdm</#if>
)
select 	 t1.sqyy      --社区医院
		,t2.sqzl      --社区总量
		,t3.qjyy      --区级医院
		,t4.qjzl      --区级总量
		,t5.sjyy      --市级医院
		,t6.sjzl      --市级总量
from t1
cross join t2
cross join t3
cross join t4
cross join t5
cross join t6;


--参与机构数量（文字） 优化版

with base_data as (
select
        sum(case when levels = '1' and examined = '1' then 1 else 0 end) as sqyy,  -- 社区医院
        sum(case when levels = '1' then 1 else 0 end) as sqzl,                    -- 社区总量
        sum(case when levels = '2' and examined = '1' then 1 else 0 end) as qjyy, -- 区级医院
        sum(case when levels = '2' then 1 else 0 end) as qjzl,                    -- 区级总量
        sum(case when levels = '3' and examined = '1' then 1 else 0 end) as sjyy, -- 市级医院
        sum(case when levels = '3' then 1 else 0 end) as sjzl                     -- 市级总量
    from cendq.T_DEF_YLJGDM_COMPARISON
    where 1=1
        <#if qxdm?? && qxdm != ""> and QXDM = :qxdm</#if>
)
select 	 
    t1.sqyy,      --社区医院
	t1.sqzl,      --社区总量
	t1.qjyy,      --区级医院
	t1.qjzl,      --区级总量
	t1.sjyy,      --市级医院
	t1.sjzl      --市级总量
from base_data t1 ;

--参与机构数 图表 原版
select 
	t1.shjqj,
	t1.ywrq,
	t1.levels,
	t1.flag,
	count(1) as cnt
from (
	select 	
		jg.shjqj,
		jg.ywrq,
		zd.levels,
		jg.jgpf,
		case when jg.jgpf < 60 then '0-60分'
			 when jg.jgpf < 70 then '60-70分'
			 when jg.jgpf < 80 then '70-80分'
			 when jg.jgpf < 85 then '80-85分'
			 when jg.jgpf < 95 then '85-95分'
		else 
		     '95分以上'
		end as flag
	from cendq_tj.TB_DQ_KHBG_JG jg
	left join cendq.T_DEF_YLJGDM_COMPARISON zd
	on jg.JGDM = zd.FJ_YLJGYQDM
	where shjqj = :shjqj
	and ywrq = :ywrq
	and zd.examined = '1'
	<#if qxdm?? && qxdm != ""> AND zd.QXDM=:qxdm</#if>
) t1
group by 
t1.shjqj,
t1.ywrq,
t1.levels,
t1.flag;

--参与机构数 图表 优化
with jgpf_range as (
    select
        jg.shjqj,
        jg.ywrq,
        zd.levels,  
        jg.jgpf,
        case when jg.jgpf < 60 then '0-60分'
             when jg.jgpf < 70 then '60-70分'
             when jg.jgpf < 80 then '70-80分'
             when jg.jgpf < 85 then '80-85分'
             when jg.jgpf < 95 then '85-95分'
        else '95分以上'
        end as flag
    from cendq_tj.TB_DQ_KHBG_JG jg
    left join cendq.T_DEF_YLJGDM_COMPARISON zd
       on jg.JGDM = zd.FJ_YLJGYQDM 
    where zd.examined = '1' 
        and shjqj = :shjqj
        and ywrq = :ywrq
        <#if qxdm?? && qxdm != ""> AND zd.QXDM=:qxdm</#if>
)
select
    t1.shjqj,
    t1.ywrq,
    t1.levels,
    t1.flag,
    count(1) as cnt
from jgpf_range t1
group by t1.shjqj,t1.ywrq,t1.levels,t1.flag;

--区排名详情 原版
WITH t1 AS (
    SELECT 
        zd.qxdm,
        zd.ssqxmc qxmc,   --  所属区域名称
        round(nvl(AVG(jg.LXXDF),0),2) LXXDF,
        round(nvl(AVG(jg.HFXDF),0),2) HFXDF,
        round(nvl(AVG(jg.JSXDF),0),2) JSXDF,
        round(nvl(AVG(jg.ZDWZXDF),0),2) ZDWZXDF,
        round(nvl(AVG(jg.BDWZXDF),0),2) BDWZXDF,
        round(nvl(AVG(jg.YJZKZQXDF),0),2) YJZKZQXDF,
        round(nvl(AVG(jg.NHZKZQXDF),0),2) NHZKZQXDF,
        round(nvl(AVG(jg.GLXDF),0),2) GLXDF,
        '0' zxzkdf,
        round(nvl(AVG(jg.jgpf),0),2) zf  --机构评分 
        
    FROM cendq_tj.TB_DQ_KHBG_JG jg
    LEFT JOIN cendq.T_DEF_YLJGDM_COMPARISON zd ON jg.JGDM = zd.FJ_YLJGYQDM
    WHERE zd.examined = '1'
        AND shjqj = :shjqj
        AND ywrq = :ywrq
    GROUP BY zd.qxdm, zd.ssqxmc
)
SELECT 
    qxdm,
    qxmc,
    lxxdf,
    hfxdf,
    jsxdf,
    zdwzxdf,
    bdwzxdf,
    yjzkzqxdf,
    nhzkzqxdf,
    glxdf,
    '0' zxzkdf,      --专项质控得分
    zf,
    row_number () over (order by zf DESC) as rn
FROM t1
ORDER BY rn;

--区排名详情 优化版
WITH t1 AS (
    SELECT 
        zd.qxdm,
        zd.ssqxmc,   --  所属区域名称
        round(nvl(AVG(jg.LXXDF),0),2) LXXDF,
        round(nvl(AVG(jg.HFXDF),0),2) HFXDF,
        round(nvl(AVG(jg.JSXDF),0),2) JSXDF,
        round(nvl(AVG(jg.ZDWZXDF),0),2) ZDWZXDF,
        round(nvl(AVG(jg.BDWZXDF),0),2) BDWZXDF,
        round(nvl(AVG(jg.YJZKZQXDF),0),2) YJZKZQXDF,
        round(nvl(AVG(jg.NHZKZQXDF),0),2) NHZKZQXDF,
        round(nvl(AVG(jg.GLXDF),0),2) GLXDF,
        '0' zxzkdf,
        round(nvl(AVG(jg.jgpf),0),2) zf  --机构评分 
    FROM cendq_tj.TB_DQ_KHBG_JG jg
    LEFT JOIN cendq.T_DEF_YLJGDM_COMPARISON zd 
    ON jg.JGDM = zd.FJ_YLJGYQDM 
    WHERE zd.examined = '1'
        AND shjqj = :shjqj
        AND ywrq = :ywrq
    GROUP BY zd.qxdm, zd.ssqxmc
)
SELECT 
    qxdm,             --区县代码
    ssqxmc as qxmc,             --区县名称
    lxxdf,            --连续性得分
    hfxdf,            --合法性得分
    jsxdf,            --及时性得分
    zdwzxdf,          --字段完整性得分
    bdwzxdf,          --表单完整性得分
    yjzkzqxdf,        --一级质控准确性得分
    nhzkzqxdf,        --内涵质控准确性得分
    glxdf,            --关联性得分
    '0' zxzkdf,       --专项质控得分
    zf,               --机构评分
    row_number () over (order by zf DESC) as rn  --排名
FROM t1
ORDER BY rn;

--区分数排名 原版
select 
    	shjqj,
    	ywrq,
		qxdm,
    	ssqxmc,   --所属区域名称
    	round(AVG_JGPF,2) qxpjdf,
    	row_number () over (partition by shjqj,ywrq order by AVG_JGPF DESC) as rn
    from (
    	select 	
    		jg.shjqj,
    		jg.ywrq,
			zd.qxdm,
    		zd.ssqxmc,
    		avg(jg.jgpf) as AVG_JGPF
    	from cendq_tj.TB_DQ_KHBG_JG jg
    	left join cendq.T_DEF_YLJGDM_COMPARISON zd
    	on jg.JGDM = zd.FJ_YLJGYQDM
    	where zd.examined = '1'
		and	shjqj = :shjqj
    	and ywrq = :ywrq
    	 <#if qxdm?? && qxdm != ""> AND zd.QXDM=:qxdm</#if>
    	group by jg.shjqj,
    			 jg.ywrq,
				 zd.qxdm,
    			 zd.ssqxmc
    ) t1
order by round(AVG_JGPF,2) desc;

--区分数排名 优化版
with avg_jgpf as (
    select
        jg.shjqj,
        jg.ywrq,
        zd.qxdm,
        zd.ssqxmc,
        avg(jg.jgpf) as AVG_JGPF
    from cendq_tj.TB_DQ_KHBG_JG jg
    left join cendq.T_DEF_YLJGDM_COMPARISON zd
    on jg.JGDM = zd.FJ_YLJGYQDM
    where zd.examined = '1'
    and shjqj = :shjqj
    and ywrq = :ywrq
    <#if qxdm?? && qxdm != ""> AND zd.QXDM=:qxdm</#if>
    group by jg.shjqj,jg.ywrq,zd.qxdm,zd.ssqxmc
)
select
    t1.shjqj,
    t1.ywrq,
    t1.qxdm,
    t1.ssqxmc,
    round(t1.AVG_JGPF,2) as qxpjdf,  --区县评级得分
    row_number () over (partition by t1.shjqj,t1.ywrq order by t1.AVG_JGPF DESC) as rn
from avg_jgpf t1
order by t1.AVG_JGPF desc;


--业务数据量 原版
with main as(
select 
		 sum(a.JYJLSH) sjl
		,b.qxdm
		,b.ssqxmc
		<#if qxdm?? && qxdm != "">,b.fj_yljgyqdm, b.fj_yljgmc </#if>
		<#if jgdm?? && jgdm != "">,c.bm, c.BM_ZH </#if>
    from cendq_tj.TB_DQ_AMOUNTOFDATA_YWSJ a
    inner join cendq.T_DEF_YLJGDM_COMPARISON b
        on a.JGDM = b.fj_yljgyqdm
        <#if jgdm?? && jgdm != "">inner join cendq_tj.T_DIC_TABLE_LIST c on a.BM = c.BM
        </#if>
where a.ywrq between :pre_ywrq and :ywrq
<#if qxdm?? && qxdm != "">and b.qxdm = :qxdm  and b.examined = '1'</#if>
<#if jgdm?? && jgdm != "">and b.fj_yljgyqdm = :jgdm </#if>
group by b.qxdm , b.ssqxmc 
<#if qxdm?? && qxdm != "">,b.fj_yljgyqdm, b.fj_yljgmc</#if>
<#if jgdm?? && jgdm != "">,c.bm, c.BM_ZH </#if>)
select 	 sjl
		,qxdm
		,ssqxmc
		<#if qxdm?? && qxdm != "">,fj_yljgyqdm, fj_yljgmc </#if>
		<#if jgdm?? && jgdm != "">,bm, BM_ZH </#if>
		,rownum rn
from main;




