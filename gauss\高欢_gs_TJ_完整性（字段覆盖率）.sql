
SET hive.execution.engine = tez;
SET yarn.timeline-service.enabled;
set tez.am.resource.memory.mb=8192;
SET hive.query.timeout.seconds=3600s;






--T_PERSONAL_RESERVATION 个人体检信息
insert INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.jgdm as jgdm,
  'F0036' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
	current_timestamp as xgshj
from prefix_dq_tj.tb_ods_ywltj_detail a
left join prefix_dq_tj.tb_sjlmx_ywsj_tj b
	ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='T_PERSONAL_RESERVATION'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='T_PERSONAL_RESERVATION' or a.tbname is null);

--T_PERSONAL_RESERVATION_TOTAL 个人体检项目总表
insert INTO hive_wsjk_v12_prod_source_dwd_test.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.jgdm as jgdm,
  'F0037' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
  CURRENT_TIMESTAMP
from prefix_dq_tj.tb_ods_ywltj_detail a
LEFT join prefix_dq_tj.tb_sjlmx_ywsj_tj b
ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='T_PERSONAL_RESERVATION_TOTAL'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='T_PERSONAL_RESERVATION_TOTAL' or a.tbname is null);


--T_EXAMINATION_QNA_INFO 体检健康问卷采集表
insert INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.jgdm as jgdm,
  'F0038' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
  CURRENT_TIMESTAMP
from prefix_dq_tj.tb_ods_ywltj_detail a
LEFT join prefix_dq_tj.tb_sjlmx_ywsj_tj b
ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='T_EXAMINATION_QNA_INFO'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='T_EXAMINATION_QNA_INFO' or a.tbname is null);

--T_EXAMINATION_BASIC_RESULTS 体检一般检查结果表
insert INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.jgdm as jgdm,
  'F0039' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
  CURRENT_TIMESTAMP
from prefix_dq_tj.tb_ods_ywltj_detail a
LEFT join prefix_dq_tj.tb_sjlmx_ywsj_tj b
ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='T_EXAMINATION_BASIC_RESULTS'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='T_EXAMINATION_BASIC_RESULTS' or a.tbname is null);


--T_EXAMINATION_RESULTS 体检物理检查科室结论表
insert INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.jgdm as jgdm,
  'F0040' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
  CURRENT_TIMESTAMP
from prefix_dq_tj.tb_ods_ywltj_detail a
LEFT join prefix_dq_tj.tb_sjlmx_ywsj_tj b
ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='T_EXAMINATION_RESULTS'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='T_EXAMINATION_RESULTS' or a.tbname is null);

--T_EXAMINATION_RESULTS_DETAIL 体检物理检查结果明细表
insert INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.jgdm as jgdm,
  'F0041' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
  CURRENT_TIMESTAMP
from prefix_dq_tj.tb_ods_ywltj_detail a
LEFT join prefix_dq_tj.tb_sjlmx_ywsj_tj b
ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='T_EXAMINATION_RESULTS_DETAIL'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='T_EXAMINATION_RESULTS_DETAIL' or a.tbname is null);

--T_EXAMINATION_SUPPORTIVE_RESULTS 体检辅助检查放射、超声、内镜、病理、心电图、功能检查等）结果表
insert INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.jgdm as jgdm,
  'F0042' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
  CURRENT_TIMESTAMP
from prefix_dq_tj.tb_ods_ywltj_detail a
LEFT join prefix_dq_tj.tb_sjlmx_ywsj_tj b
ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='T_EXAMINATION_SUPPORTIVE_RESULTS'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='T_EXAMINATION_SUPPORTIVE_RESULTS' or a.tbname is null);

--TB_TJ_LIS_REPORT 体检实验室检验报告表头
insert INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.jgdm as jgdm,
  'F0043' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
  CURRENT_TIMESTAMP
from prefix_dq_tj.tb_ods_ywltj_detail a
LEFT join prefix_dq_tj.tb_sjlmx_ywsj_tj b
ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='TB_TJ_LIS_REPORT'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='TB_TJ_LIS_REPORT' or a.tbname is null);


--TB_TJ_LIS_INDICATORS 体检实验室结果指标表
insert INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.jgdm as jgdm,
  'F0044' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
  CURRENT_TIMESTAMP
from prefix_dq_tj.tb_ods_ywltj_detail a
LEFT join prefix_dq_tj.tb_sjlmx_ywsj_tj b
ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='TB_TJ_LIS_INDICATORS'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='TB_TJ_LIS_INDICATORS' or a.tbname is null);

--T_POSITIVE_AUDIT 重要异常结果记录
insert INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.jgdm as jgdm,
  'F0045' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
  CURRENT_TIMESTAMP
from prefix_dq_tj.tb_ods_ywltj_detail a
LEFT join prefix_dq_tj.tb_sjlmx_ywsj_tj b
ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='T_POSITIVE_AUDIT'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='T_POSITIVE_AUDIT' or a.tbname is null);


--T_AUDIT_FINAL_STATUS 主检报告记录
insert INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywsj as ywsj,
  a.YLJGYQDM as jgdm,
  'F0046' gzbh,
  (case when coalesce(b.sjl,0)=0 and a.bz=0 then '0'
        else '1' end) as JYJLSH,
  (case when coalesce(b.sjl,0)>0 then '1'
        else '0' end) as FHGZSH,
	'v2.0' as bbh,
	current_timestamp as xgshj
from prefix_dq_tj.tb_ods_ywltj_detail a
left join prefix_dq_tj.tb_sjlmx_ywsj_tj b
	ON a.posid ='posid_'||substr(b.dbname FROM 3 FOR 5) and a.ywsj=b.ywrq and b.tbname='T_AUDIT_FINAL_STATUS'
where a.ywsj >= '${BTIME}' AND a.ywsj < '${ETIME}' and (upper(a.tbname)='T_AUDIT_FINAL_STATUS' or a.tbname is null);