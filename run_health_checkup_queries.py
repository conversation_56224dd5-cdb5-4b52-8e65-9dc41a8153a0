import os
import sys
from health_checkup_db_connection import HealthCheckupDB
from db_config import DEFAULT_CONFIG

def execute_sql_file(db, sql_file_path, description=""):
    """
    执行SQL文件
    """
    print(f"\n📋 执行SQL文件: {sql_file_path}")
    if description:
        print(f"描述: {description}")
    
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as file:
            sql_content = file.read()
        
        # 执行SQL
        result = db.execute_query(sql_content)
        if result is not None:
            print(f"✅ 查询成功，返回 {len(result)} 行数据")
            print("前5行数据:")
            print(result.head())
            return result
        else:
            print("❌ 查询失败")
            return None
            
    except Exception as e:
        print(f"❌ 执行SQL文件失败: {str(e)}")
        return None

def main():
    """
    主函数 - 执行健康体检相关的SQL查询
    """
    print("🏥 健康体检数据查询工具")
    print("=" * 60)
    
    # 创建数据库连接
    db = HealthCheckupDB(**DEFAULT_CONFIG)
    
    # 连接数据库
    if not db.connect():
        print("❌ 无法连接到数据库，请检查配置")
        return
    
    try:
        # 示例1: 查询数据库基本信息
        print("\n📊 数据库基本信息:")
        info_sql = """
        SELECT 
            @@VERSION as 数据库版本,
            DB_NAME() as 当前数据库,
            GETDATE() as 当前时间,
            @@SERVERNAME as 服务器名称
        """
        result = db.execute_query(info_sql)
        if result is not None:
            print(result)
        
        # 示例2: 查询所有表
        print("\n📋 数据库中的所有表:")
        tables_sql = """
        SELECT 
            TABLE_SCHEMA as 架构名,
            TABLE_NAME as 表名,
            TABLE_TYPE as 表类型
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_SCHEMA, TABLE_NAME
        """
        result = db.execute_query(tables_sql)
        if result is not None:
            print(result)
        
        # 示例3: 执行项目中的SQL文件
        print("\n🔍 执行项目SQL文件:")
        
        # 查找项目中的SQL文件
        sql_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.sql'):
                    sql_files.append(os.path.join(root, file))
        
        print(f"找到 {len(sql_files)} 个SQL文件:")
        for i, sql_file in enumerate(sql_files[:5], 1):  # 只显示前5个
            print(f"  {i}. {sql_file}")
        
        # 示例4: 执行特定的SQL文件（如果有的话）
        # 这里可以根据实际需要执行特定的SQL文件
        # 例如：execute_sql_file(db, '项目_健康体检/sqlserver/业务量明细sjlmx_ss.sql', '业务量明细查询')
        
        # 示例5: 查询表结构
        print("\n📋 查询表结构示例:")
        # 假设有一个名为 'health_checkup' 的表
        # table_info = db.get_table_info('health_checkup')
        # if table_info is not None:
        #     print(table_info)
        
        # 示例6: 导出数据到Excel
        print("\n📊 导出数据示例:")
        # 示例查询
        sample_sql = """
        SELECT TOP 10 
            '示例数据' as 数据类型,
            GETDATE() as 查询时间,
            @@VERSION as 数据库版本
        """
        # db.export_to_excel(sample_sql, 'sample_data.xlsx', '示例数据')
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {str(e)}")
    
    finally:
        # 关闭数据库连接
        db.disconnect()

def interactive_mode():
    """
    交互模式 - 允许用户输入SQL查询
    """
    print("\n🔧 交互模式")
    print("输入 'quit' 退出，输入 'help' 查看帮助")
    
    db = HealthCheckupDB(**DEFAULT_CONFIG)
    if not db.connect():
        return
    
    try:
        while True:
            print("\n" + "-" * 40)
            sql_input = input("请输入SQL查询: ").strip()
            
            if sql_input.lower() == 'quit':
                break
            elif sql_input.lower() == 'help':
                print("帮助信息:")
                print("- 输入SQL查询语句")
                print("- 输入 'quit' 退出")
                print("- 输入 'help' 查看帮助")
                continue
            elif not sql_input:
                continue
            
            # 执行SQL
            result = db.execute_query(sql_input)
            if result is not None:
                print(f"查询结果 ({len(result)} 行):")
                print(result)
            else:
                print("查询失败")
    
    finally:
        db.disconnect()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--interactive':
        interactive_mode()
    else:
        main()
    
    print("\n" + "=" * 60)
    print("📝 使用说明:")
    print("1. 修改 db_config.py 中的数据库连接参数")
    print("2. 运行 python run_health_checkup_queries.py 执行预设查询")
    print("3. 运行 python run_health_checkup_queries.py --interactive 进入交互模式")
    print("4. 可以修改代码来执行项目中的特定SQL文件") 