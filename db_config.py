# 数据库连接配置
# 请根据您的实际环境修改以下参数

# SQL Server 连接配置
SQLSERVER_CONFIG = {
    'server': 'localhost',  # 服务器名称或IP地址
    'database': 'health_checkup_db',  # 数据库名称
    'username': 'sa',  # 用户名
    'password': 'your_password',  # 密码
    'port': 1433  # 端口号
}

# 开发环境配置
DEV_CONFIG = {
    'server': 'dev-server',
    'database': 'health_checkup_dev',
    'username': 'dev_user',
    'password': 'dev_password',
    'port': 1433
}

# 测试环境配置
TEST_CONFIG = {
    'server': 'test-server',
    'database': 'health_checkup_test',
    'username': 'test_user',
    'password': 'test_password',
    'port': 1433
}

# 生产环境配置
PROD_CONFIG = {
    'server': 'prod-server',
    'database': 'health_checkup_prod',
    'username': 'prod_user',
    'password': 'prod_password',
    'port': 1433
}

# 默认使用开发环境配置
DEFAULT_CONFIG = DEV_CONFIG

# 连接超时设置
CONNECTION_TIMEOUT = 30  # 秒
QUERY_TIMEOUT = 60  # 秒

# 字符编码
CHARSET = 'utf8'

# 日志配置
LOG_LEVEL = 'INFO'
LOG_FILE = 'db_operations.log' 