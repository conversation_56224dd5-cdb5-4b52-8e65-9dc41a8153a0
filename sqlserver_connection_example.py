import pymssql
import pandas as pd
from datetime import datetime

def connect_sqlserver():
    """
    连接 SQL Server 数据库的示例函数
    """
    try:
        # 数据库连接参数
        server = 'your_server_name'  # 服务器名称或IP地址
        database = 'your_database_name'  # 数据库名称
        username = 'your_username'  # 用户名
        password = 'your_password'  # 密码
        port = 1433  # 端口号（默认1433）
        
        # 建立连接
        conn = pymssql.connect(
            server=server,
            database=database,
            user=username,
            password=password,
            port=port,
            charset='utf8'
        )
        
        print("✅ 数据库连接成功！")
        return conn
        
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return None

def execute_query(conn, sql_query):
    """
    执行 SQL 查询并返回结果
    """
    try:
        cursor = conn.cursor()
        cursor.execute(sql_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据
        rows = cursor.fetchall()
        
        # 转换为 DataFrame
        df = pd.DataFrame(rows, columns=columns)
        
        cursor.close()
        return df
        
    except Exception as e:
        print(f"❌ 查询执行失败: {str(e)}")
        return None

def execute_non_query(conn, sql_query):
    """
    执行非查询 SQL（如 INSERT, UPDATE, DELETE）
    """
    try:
        cursor = conn.cursor()
        cursor.execute(sql_query)
        conn.commit()
        affected_rows = cursor.rowcount
        cursor.close()
        
        print(f"✅ 执行成功，影响行数: {affected_rows}")
        return affected_rows
        
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
        conn.rollback()
        return 0

def test_connection():
    """
    测试连接和基本操作
    """
    # 连接数据库
    conn = connect_sqlserver()
    if conn is None:
        return
    
    try:
        # 示例1: 查询数据库版本
        print("\n📋 查询数据库版本:")
        version_query = "SELECT @@VERSION as version"
        result = execute_query(conn, version_query)
        if result is not None:
            print(result)
        
        # 示例2: 查询当前时间
        print("\n📋 查询当前时间:")
        time_query = "SELECT GETDATE() as current_time"
        result = execute_query(conn, time_query)
        if result is not None:
            print(result)
        
        # 示例3: 查询数据库名称
        print("\n📋 查询当前数据库:")
        db_query = "SELECT DB_NAME() as database_name"
        result = execute_query(conn, db_query)
        if result is not None:
            print(result)
            
    finally:
        # 关闭连接
        conn.close()
        print("\n🔒 数据库连接已关闭")

def connect_with_connection_string():
    """
    使用连接字符串连接数据库
    """
    try:
        # 连接字符串格式
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=your_server_name;"
            "DATABASE=your_database_name;"
            "UID=your_username;"
            "PWD=your_password;"
        )
        
        # 注意：连接字符串方式需要安装 pyodbc
        # import pyodbc
        # conn = pyodbc.connect(connection_string)
        
        print("连接字符串方式需要安装 pyodbc")
        
    except Exception as e:
        print(f"连接失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 SQL Server 数据库连接示例")
    print("=" * 50)
    
    # 测试连接
    test_connection()
    
    print("\n" + "=" * 50)
    print("📝 使用说明:")
    print("1. 修改 connect_sqlserver() 函数中的连接参数")
    print("2. 根据您的数据库结构编写相应的 SQL 查询")
    print("3. 使用 execute_query() 执行查询语句")
    print("4. 使用 execute_non_query() 执行增删改操作")
    print("5. 记得在操作完成后关闭数据库连接") 