INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )
SELECT YWRQ
 ,JGDM
 ,'40001' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,-- 使用 Hive 标准函数 COALESCE
 'v1.2' AS bbh
 ,CURRENT_TIMESTAMP AS XGSHJ -- Hive 时间函数
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.YWRQ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.TB_STAT_YWL_REPORT t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE
  round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM TO_TIMESTAMP(t1.ywrq || '235959', 'YYYYMMDDHH24MISS'))::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.YWRQ
   ,t2.FJ_YLJGYQDM


  ) a
 ) b
WHERE rn <= 10;





INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (ywrq,jgdm,gzbh,cwz,cwl,bbh,xgshj)
SELECT 
      YWRQ,
      JGDM,
      '40002' AS GZBH,
      cwz,
      COALESCE(CWL, 0) AS CWL,
      'v1.2' AS bbh,
      CURRENT_TIMESTAMP AS XGSHJ -- Hive 时间函数
FROM (
    SELECT 
            YWRQ,
            JGDM,
            cwz,
            CWL,
            ROW_NUMBER() OVER (partition by ywrq,jgdm order by CWL desc) AS rn
    FROM (
          SELECT 
                t1.YWRQ,
                t2.FJ_YLJGYQDM AS JGDM,
                t1.YWRQ AS cwz,
                COUNT(1) AS CWL
          FROM DWD.tb_stat_ywsr_report t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
          WHERE
                round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM TO_TIMESTAMP(t1.ywrq || '235959', 'YYYYMMDDHH24MISS'))::BIGINT ) / 86400.0, 2) > 1
          GROUP BY t1.YWRQ,t2.FJ_YLJGYQDM
      ) a
 ) b
WHERE rn <= 10;

INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )
SELECT YWRQ
 ,JGDM
 ,'40003' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,-- 使用 Hive 标准函数 COALESCE
 'v1.2' AS bbh
 ,CURRENT_TIMESTAMP AS XGSHJ -- Hive 时间函数
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.YWRQ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_stat_lis_report t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE
  round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM TO_TIMESTAMP(t1.ywrq || '235959', 'YYYYMMDDHH24MISS'))::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.YWRQ
   ,t2.FJ_YLJGYQDM

  ) a
 ) b
WHERE rn <= 10;

INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
    ywrq,
    jgdm,
    gzbh,
    cwz,
    cwl,
    bbh,
    xgshj
  )
SELECT YWRQ,
  JGDM,
  '40004' AS GZBH,
  cwz,
  COALESCE(CWL, 0) AS CWL,
  -- 使用 Hive 标准函数 COALESCE
  'v1.2' AS bbh,
  CURRENT_TIMESTAMP AS XGSHJ -- Hive 时间函数
FROM (
    SELECT 
      YWRQ,
      JGDM,
      cwz,
      CWL,
      ROW_NUMBER() OVER (partition by ywrq,jgdm order by CWL desc) AS rn
    FROM (
        SELECT t1.YWRQ,
          t2.FJ_YLJGYQDM AS JGDM,
          t1.YWRQ AS cwz,
          COUNT(1) AS CWL
        FROM DWD.tb_stat_ris_report t1
          LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM TO_TIMESTAMP(t1.ywrq || '235959', 'YYYYMMDDHH24MISS'))::BIGINT) / 86400.0,2) > 1
        GROUP BY t1.YWRQ,
          t2.FJ_YLJGYQDM
      ) a
  ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )
SELECT YWRQ
 ,JGDM
 ,'40005' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.GTHSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.GTHSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_his_mz_reg t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.GTHSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.GTHSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;



INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )

SELECT YWRQ
 ,JGDM
 ,'40006' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.STFSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.STFSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_his_mz_charge t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.STFSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.STFSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;



INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )
SELECT YWRQ
 ,JGDM
 ,'40007' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.RYSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.RYSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_his_zy_adm_reg t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.RYSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.RYSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;



INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40008' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.CYSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.CYSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_his_zy_dis_reg t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.CYSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.CYSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;



INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40009' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.STFSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.STFSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_his_jz_charge t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.STFSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.STFSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40010' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.JZKSRQ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.JZKSRQ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_yl_mz_medical_record t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.JZKSRQ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.JZKSRQ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40011' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.KFRQ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.KFRQ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_cis_prescription_detail t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.KFRQ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.KFRQ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40012' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.STFSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.STFSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_his_mz_fee_detail t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.STFSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.STFSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40013' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.CYSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.CYSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_yl_zy_medical_record t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.CYSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.CYSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40014' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SHSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SHSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_cis_dradvice_detail t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SHSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SHSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40015' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.STFSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.STFSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_his_zy_fee_detail t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.STFSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.STFSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40016' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SHSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SHSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_lis_report t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SHSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SHSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40017' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SHSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SHSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_lis_indicators t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SHSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SHSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40018' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SHSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SHSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_lis_bacteria_result t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SHSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SHSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;



INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40019' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SHSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SHSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_lis_allergy_result t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SHSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SHSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40020' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SHSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SHSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_ris_report t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SHSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SHSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40021' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SHSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SHSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_ris_report2 t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SHSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SHSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;



INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40022' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SSJSSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SSJSSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_opration_detail t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SSJSSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SSJSSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40023' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.ZDSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.ZDSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_ih_diagnosis_detail t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.ZDSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.ZDSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;



INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40024' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.CYSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.CYSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_cis_leavehospital_summary t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.CYSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.CYSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40025' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.FYFSSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.FYFSSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_his_zy_fee_detail_fs t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.FYFSSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.FYFSSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40026' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SSJSSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SSJSSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_his_opr_rec t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SSJSSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SSJSSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40027' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SJJZSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SJJZSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_his_appointment t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SJJZSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SJJZSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;



INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40028' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.YWRQ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.YWRQ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.TB_STAT_APPOINTMENT_REPORT t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM TO_TIMESTAMP(t1.ywrq || '235959', 'YYYYMMDDHH24MISS'))::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.YWRQ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40029' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SHSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SHSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.TB_RIS_REPORT_DETAIL t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SHSJ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SHSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40030' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.GDRQ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.GDRQ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_ba_syjbk t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.GDRQ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.GDRQ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40031' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.GDRQ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.GDRQ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_ba_syzdk t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.GDRQ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.GDRQ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;



INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40032' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.GDRQ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.GDRQ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_ba_syssk t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.GDRQ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.GDRQ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;


INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40033' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.GDRQ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.GDRQ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_ba_syyek t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.GDRQ)::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.GDRQ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;



INSERT INTO prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
 ywrq
 ,jgdm
 ,gzbh
 ,cwz
 ,cwl
 ,bbh
 ,xgshj
 )


SELECT YWRQ
 ,JGDM
 ,'40034' AS GZBH
 ,cwz
 ,COALESCE(CWL, 0) AS CWL
 ,'v1.2' AS bbh
 ,TO_TIMESTAMP(EXTRACT(EPOCH FROM NOW())) AS XGSHJ
FROM (
 SELECT YWRQ
  ,JGDM
  ,cwz
  ,CWL
  ,ROW_NUMBER() OVER (
   partition by ywrq,jgdm order by CWL desc
   ) AS rn
 FROM (
  SELECT t1.SHSJ AS YWRQ
   ,t2.FJ_YLJGYQDM AS JGDM
   ,t1.SHSJ AS cwz
   ,COUNT(1) AS CWL
  FROM DWD.tb_genetic_report t1
  LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
  WHERE

   round((EXTRACT(EPOCH FROM t1.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM t1.SHSJ )::BIGINT ) / 86400.0, 2) > 1
  GROUP BY t1.SHSJ
   ,t2.FJ_YLJGYQDM
  ) a
 ) b
WHERE rn <= 10;