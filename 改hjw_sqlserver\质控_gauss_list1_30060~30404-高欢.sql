INSERT INTO prefix_dq.dbo.TB_DQ_INDICATOR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        jyjlsh,
        fhgzsh,
        BBH,
        xgshj
    )
SELECT FORMAT(
        DATEADD(DAY, - 1, CAST(GETDATE() AS DATE)),
        'yyyyMMdd'
    ) YWRQ,
    a.JGDM,
    '30060' GZBH,
    a.JYJLSH,
(ISNULL(a.FHGZSH, 0)) FHGZSH,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT t2.FJ_YLJGYQDM JGDM,
            COUNT(1) JYJLSH,
            SUM(
                CASE
                    -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                    WHEN trim(t1.KLX) = '0'
                    AND PATINDEX('[^0-9A-Z]', t1.KH) = 0 
                    AND datalength(t1.KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                    WHEN trim(t1.KLX) = '1'
                    AND PATINDEX('[^0-9]', t1.KH) = 0
                    AND datalength(t1.KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                    WHEN trim(t1.KLX) = '2'
                    AND PATINDEX('[^0-9]', t1.KH) = 0
                    AND datalength(t1.KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                    WHEN trim(t1.KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                    WHEN trim(t1.KLX) = '9' THEN 1 -- 默认情况
                    ELSE 0
                END
            ) FHGZSH
        FROM DWD.dbo.tb_yl_patient_information t1
            LEFT JOIN (
                SELECT yljgyqdm,
                    FJ_YLJGYQDM
                FROM prefix_dq.dbo.T_DIC_YLJGDM_COMPARISON
            ) t2 ON t1.YLJGYQDM = t2.yljgyqdm
        WHERE TRIM(t1.KLX) IN (
                '0',
                '1',
                '2',
                '3',
                '4',
                '9'
            )
            AND t1.XGBZ = '1' --AND t1.bbh = 'v1.2'
        GROUP BY t2.FJ_YLJGYQDM
    ) a;



INSERT INTO prefix_dq.dbo.TB_DQ_INDICATOR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        jyjlsh,
        fhgzsh,
        BBH,
        xgshj
    )
SELECT FORMAT(
        DATEADD(DAY, - 1, CAST(GETDATE() AS DATE)),
        'yyyyMMdd'
    ) YWRQ,
    JGDM,
    '30074' GZBH,
    JYJLSH,
(ISNULL(FHGZSH, 0)) FHGZSH,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT t2.FJ_YLJGYQDM JGDM,
            COUNT(1) JYJLSH,
            SUM(
                CASE
                    -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                    WHEN trim(t1.KLX) = '0'
                    AND PATINDEX('[^0-9A-Z]', t1.KH) = 0 
                    AND datalength(t1.KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                    WHEN trim(t1.KLX) = '1'
                    AND PATINDEX('[^0-9]', t1.KH) = 0
                    AND datalength(t1.KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                    WHEN trim(t1.KLX) = '2'
                    AND PATINDEX('[^0-9]', t1.KH) = 0
                    AND datalength(t1.KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                    WHEN trim(t1.KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                    WHEN trim(t1.KLX) = '9' THEN 1 -- 默认情况
                    ELSE 0
                END
            ) FHGZSH
        FROM DWD.dbo.TB_RIS_PATIENT_INFORMATION t1
            LEFT JOIN (
                SELECT yljgyqdm,
                    FJ_YLJGYQDM
                FROM prefix_dq.dbo.T_DIC_YLJGDM_COMPARISON
            ) t2 ON t1.YLJGYQDM = t2.yljgyqdm
        WHERE TRIM(t1.KLX) IN (
                '0',
                '1',
                '2',
                '3',
                '4',
                '9'
            )
            AND t1.XGBZ = '1' --AND t1.bbh = 'v1.2'
        GROUP BY t2.FJ_YLJGYQDM
    ) a;