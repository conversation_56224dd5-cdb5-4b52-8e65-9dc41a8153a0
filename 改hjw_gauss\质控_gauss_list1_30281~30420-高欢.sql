insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30281' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.RYRQ ~ '^\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{2}:\\d{2}:\\d{2}$' THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;


insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30282' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.QZRQ ~ '^\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])$' THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.QZRQ is not null
            and b.QZRQ != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;

--入院科室符合率


insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30283' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.RYKS IN (
                        '01',
                        '02',
                        '03',
                        '0301',
                        '0302',
                        '0303',
                        '0304',
                        '030401',
                        '030402',
                        '0305',
                        '0306',
                        '0307',
                        '030701',
                        '030702',
                        '0308',
                        '030801',
                        '030802',
                        '030803',
                        '030804',
                        '0309',
                        '0310',
                        '0311',
                        '031102',
                        '031101',
                        '04',
                        '0401',
                        '040101',
                        '040102',
                        '040103',
                        '040104',
                        '040105',
                        '040106',
                        '040107',
                        '04010701',
                        '040108',
                        '040109',
                        '0402',
                        '0403',
                        '040301',
                        '040302',
                        '04030201',
                        '040303',
                        '0404',
                        '040401',
                        '040402',
                        '0405',
                        '040501',
                        '0406',
                        '040601',
                        '0407',
                        '0408',
                        '0409',
                        '040901',
                        '05',
                        '0501',
                        '0502',
                        '0503',
                        '0504',
                        '0505',
                        '0506',
                        '06',
                        '0601',
                        '0602',
                        '0603',
                        '0604',
                        '0605',
                        '0606',
                        '07',
                        '0701',
                        '0702',
                        '0703',
                        '070301',
                        '0704',
                        '0705',
                        '0706',
                        '0707',
                        '0708',
                        '0709',
                        '0710',
                        '0711',
                        '071101',
                        '0712',
                        '08',
                        '0801',
                        '0802',
                        '0803',
                        '0804',
                        '0805',
                        '0806',
                        '080601',
                        '09',
                        '0901',
                        '0902',
                        '0903',
                        '0904',
                        '0905',
                        '0906',
                        '10',
                        '11',
                        '1101',
                        '1102',
                        '1103',
                        '1104',
                        '12',
                        '1201',
                        '1202',
                        '1203',
                        '1204',
                        '1205',
                        '120501',
                        '120502',
                        '120503',
                        '1206',
                        '1207',
                        '1208',
                        '1209',
                        '1210',
                        '1211',
                        '1212',
                        '1213',
                        '13',
                        '1301',
                        '1302',
                        '1303',
                        '14',
                        '1401',
                        '15',
                        '1501',
                        '150101',
                        '150102',
                        '150103',
                        '1502',
                        '1503',
                        '1504',
                        '1505',
                        '1506',
                        '1507',
                        '1508',
                        '16',
                        '1601',
                        '1602',
                        '1603',
                        '1604',
                        '1605',
                        '1606',
                        '1607',
                        '17',
                        '18',
                        '19',
                        '1901',
                        '190101',
                        '1902',
                        '1903',
                        '20',
                        '2001',
                        '200101',
                        '2002',
                        '200201',
                        '200202',
                        '200203',
                        '200204',
                        '200205',
                        '200206',
                        '2003',
                        '200301',
                        '200302',
                        '2004',
                        '2005',
                        '2006',
                        '2007',
                        '200701',
                        '200702',
                        '200703',
                        '2008',
                        '2009',
                        '2010',
                        '21',
                        '22',
                        '23',
                        '2301',
                        '2302',
                        '2303',
                        '2304',
                        '2305',
                        '2306',
                        '24',
                        '25',
                        '26',
                        '27',
                        '28',
                        '30',
                        '3001',
                        '3002',
                        '3003',
                        '3004',
                        '3005',
                        '3006',
                        '31',
                        '32',
                        '3201',
                        '3202',
                        '320201',
                        '3203',
                        '3204',
                        '3205',
                        '3206',
                        '3207',
                        '3208',
                        '3209',
                        '320901',
                        '320902',
                        '3210',
                        '321001',
                        '32100101',
                        '3211',
                        '50',
                        '5001',
                        '500101',
                        '500102',
                        '500103',
                        '500104',
                        '500105',
                        '500106',
                        '500107',
                        '500108',
                        '500109',
                        '500110',
                        '5002',
                        '500201',
                        '500202',
                        '500203',
                        '500204',
                        '5003',
                        '500301',
                        '500302',
                        '5004',
                        '5005',
                        '5006',
                        '5007',
                        '5008',
                        '5009',
                        '5010',
                        '501001',
                        '501002',
                        '5011',
                        '5012',
                        '5013',
                        '5014',
                        '5015',
                        '501501',
                        '501502',
                        '5016',
                        '501601',
                        '5017',
                        '5018',
                        '501801',
                        '501802',
                        '501803',
                        '501804',
                        '501805',
                        '501806',
                        '501807',
                        '501808',
                        '501809',
                        '501810',
                        '501811',
                        '501812',
                        '51',
                        '5101',
                        '5102',
                        '5103',
                        '5104',
                        '5105',
                        '5106',
                        '52',
                        '69',
                        '6901'
                    ) THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.RYKS is not null
            and b.RYKS != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;

--出院日期符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30285' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.CYRQ ~ '^\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{2}:\\d{2}:\\d{2}$' THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;

--出院科室符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30286' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.CYKS IN (
                        '01',
                        '02',
                        '03',
                        '0301',
                        '0302',
                        '0303',
                        '0304',
                        '030401',
                        '030402',
                        '0305',
                        '0306',
                        '0307',
                        '030701',
                        '030702',
                        '0308',
                        '030801',
                        '030802',
                        '030803',
                        '030804',
                        '0309',
                        '0310',
                        '0311',
                        '031102',
                        '031101',
                        '04',
                        '0401',
                        '040101',
                        '040102',
                        '040103',
                        '040104',
                        '040105',
                        '040106',
                        '040107',
                        '04010701',
                        '040108',
                        '040109',
                        '0402',
                        '0403',
                        '040301',
                        '040302',
                        '04030201',
                        '040303',
                        '0404',
                        '040401',
                        '040402',
                        '0405',
                        '040501',
                        '0406',
                        '040601',
                        '0407',
                        '0408',
                        '0409',
                        '040901',
                        '05',
                        '0501',
                        '0502',
                        '0503',
                        '0504',
                        '0505',
                        '0506',
                        '06',
                        '0601',
                        '0602',
                        '0603',
                        '0604',
                        '0605',
                        '0606',
                        '07',
                        '0701',
                        '0702',
                        '0703',
                        '070301',
                        '0704',
                        '0705',
                        '0706',
                        '0707',
                        '0708',
                        '0709',
                        '0710',
                        '0711',
                        '071101',
                        '0712',
                        '08',
                        '0801',
                        '0802',
                        '0803',
                        '0804',
                        '0805',
                        '0806',
                        '080601',
                        '09',
                        '0901',
                        '0902',
                        '0903',
                        '0904',
                        '0905',
                        '0906',
                        '10',
                        '11',
                        '1101',
                        '1102',
                        '1103',
                        '1104',
                        '12',
                        '1201',
                        '1202',
                        '1203',
                        '1204',
                        '1205',
                        '120501',
                        '120502',
                        '120503',
                        '1206',
                        '1207',
                        '1208',
                        '1209',
                        '1210',
                        '1211',
                        '1212',
                        '1213',
                        '13',
                        '1301',
                        '1302',
                        '1303',
                        '14',
                        '1401',
                        '15',
                        '1501',
                        '150101',
                        '150102',
                        '150103',
                        '1502',
                        '1503',
                        '1504',
                        '1505',
                        '1506',
                        '1507',
                        '1508',
                        '16',
                        '1601',
                        '1602',
                        '1603',
                        '1604',
                        '1605',
                        '1606',
                        '1607',
                        '17',
                        '18',
                        '19',
                        '1901',
                        '190101',
                        '1902',
                        '1903',
                        '20',
                        '2001',
                        '200101',
                        '2002',
                        '200201',
                        '200202',
                        '200203',
                        '200204',
                        '200205',
                        '200206',
                        '2003',
                        '200301',
                        '200302',
                        '2004',
                        '2005',
                        '2006',
                        '2007',
                        '200701',
                        '200702',
                        '200703',
                        '2008',
                        '2009',
                        '2010',
                        '21',
                        '22',
                        '23',
                        '2301',
                        '2302',
                        '2303',
                        '2304',
                        '2305',
                        '2306',
                        '24',
                        '25',
                        '26',
                        '27',
                        '28',
                        '30',
                        '3001',
                        '3002',
                        '3003',
                        '3004',
                        '3005',
                        '3006',
                        '31',
                        '32',
                        '3201',
                        '3202',
                        '320201',
                        '3203',
                        '3204',
                        '3205',
                        '3206',
                        '3207',
                        '3208',
                        '3209',
                        '320901',
                        '320902',
                        '3210',
                        '321001',
                        '32100101',
                        '3211',
                        '50',
                        '5001',
                        '500101',
                        '500102',
                        '500103',
                        '500104',
                        '500105',
                        '500106',
                        '500107',
                        '500108',
                        '500109',
                        '500110',
                        '5002',
                        '500201',
                        '500202',
                        '500203',
                        '500204',
                        '5003',
                        '500301',
                        '500302',
                        '5004',
                        '5005',
                        '5006',
                        '5007',
                        '5008',
                        '5009',
                        '5010',
                        '501001',
                        '501002',
                        '5011',
                        '5012',
                        '5013',
                        '5014',
                        '5015',
                        '501501',
                        '501502',
                        '5016',
                        '501601',
                        '5017',
                        '5018',
                        '501801',
                        '501802',
                        '501803',
                        '501804',
                        '501805',
                        '501806',
                        '501807',
                        '501808',
                        '501809',
                        '501810',
                        '501811',
                        '501812',
                        '51',
                        '5101',
                        '5102',
                        '5103',
                        '5104',
                        '5105',
                        '5106',
                        '52',
                        '69',
                        '6901'
                    ) THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.CYKS is not null
            and b.CYKS != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;


--中医门(急)诊诊断符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30287' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.MZZD_ZY ~ '\\d+(\.\d+)*$' THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.MZZD_ZY is not null
            and b.MZZD_ZY != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;

--实施临床路径符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30290' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.SSLCLJ in ('1', '2', '3') THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.SSLCLJ is not null
            and b.SSLCLJ != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;

--自制中医制剂符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30291' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.ZZZYZJ in ('1', '2') THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.ZZZYZJ is not null
            and b.ZZZYZJ != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;

--使用中医诊疗设备符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30292' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.ZYZLSB in ('1', '2') THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.ZYZLSB is not null
            and b.ZYZLSB != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;

--使用中医诊疗技术符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30293' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.ZYZLJS in ('1', '2') THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.ZYZLJS is not null
            and b.ZYZLJS != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;


--辨证施护符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30294' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.BZSH in ('1', '2') THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.BZSH is not null
            and b.BZSH != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;

--中医主病编码符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30295' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.ZYZD_ZY ~ '\\d+(\.\d+)*$' THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.ZYZD_ZY is not null
            and b.ZYZD_ZY != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;

--中医主证编码符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30296' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.ZYZZ_ZY ~ '\\d+(\.\d+)*$' THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.ZYZZ_ZY is not null
            and b.ZYZZ_ZY != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;


--中医入院病情符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30297' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.ZYRYBQ in ('1', '2', '3', '4') THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.ZYRYBQ is not null
            and b.ZYRYBQ != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;


--中医出院情况符合率
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30298' AS GZBH,
    a.JYJLSH,
    COALESCE(a.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    NOW() as XGSHJ
FROM (
        SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH,
            SUM(
                CASE
                    WHEN b.ZGQK_ZY in ('1', '2', '3', '4', '5') THEN 1
                    ELSE 0
                END
            ) AS FHGZSH
        FROM DWD.TB_BA_SYJBK b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.GDRQ IS NOT NULL
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --and b.bbh = 'v1.2' 
            and b.ZGQK_ZY is not null
            and b.ZGQK_ZY != ''
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.GDRQ, 'YYYYMMDD')
    ) a;

-- 基因检测报告表 (TB_GENETIC_REPORT)
-- 30349 报告日期符合率
-- BGRQ BGRQ
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30349' GZBH,
    JYJLSH,
    COALESCE((B.FHGZSH), 0) FHGZSH,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) JYJLSH
        FROM DWD.TB_GENETIC_REPORT b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
            AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}' --and b.bbh = 'v1.2' 
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COALESCE(COUNT(1), 0) FHGZSH
        FROM DWD.TB_GENETIC_REPORT b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.SHSJ IS NOT NULL
            AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
            AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}' --and b.bbh = 'v1.2' 
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ;



-- 基因检测报告表 (TB_GENETIC_REPORT)
-- 30351 门诊/住院标志符合率
-- BGRQ MZZYBZ
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30351' AS GZBH,
    A.JYJLSH,
    COALESCE(B.FHGZSH, 0) AS FHGZSH,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH
        FROM DWD.TB_GENETIC_REPORT b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
            AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}' --and b.bbh = 'v1.2'
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
            d.FJ_YLJGYQDM AS JGDM,
            COALESCE(COUNT(1), 0) AS FHGZSH
        FROM DWD.TB_GENETIC_REPORT b
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON d on b.yljgyqdm = d.yljgyqdm
        WHERE (b.MZZYBZ IN ('1', '2'))
            AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
            AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}' --and b.bbh = 'v1.2'
        GROUP BY d.FJ_YLJGYQDM,
            TO_CHAR(b.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ;



-- 卡号符合率  -- BGRQ KH
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
		YWRQ,
		JGDM,
		'30352' GZBH,
		JYJLSH,
		COALESCE(FHGZSH, 0) FHGZSH,
		'v1.2' 					bbh ,
		CURRENT_TIMESTAMP as xgshj
	FROM (SELECT
	             TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
				 t2.FJ_YLJGYQDM JGDM,
				 COUNT(*) JYJLSH,
				 SUM(CASE
			            WHEN TRIM(t1.KLX) = '0' AND t1.KH ~ '^[0-9A-Z]{9}$' THEN 1
			            WHEN TRIM(t1.KLX) = '1' AND t1.KH ~ '^[0-9]{10,10}$' THEN 1
			            WHEN TRIM(t1.KLX) = '2' AND t1.KH ~ '^[0-9]{15,15}$' THEN 1
			            WHEN TRIM(t1.KLX) = '3' AND t1.KH IS NOT NULL THEN 1
			            WHEN TRIM(t1.KLX) = '4' AND t1.KH IS NOT NULL THEN 1
			            WHEN TRIM(t1.KLX) = '9' THEN 1
			            ELSE 0
            END
        ) AS FHGZSH
					FROM DWD.TB_GENETIC_REPORT t1
					left join
					(SELECT yljgyqdm,FJ_YLJGYQDM FROM prefix_dq.T_DIC_YLJGDM_COMPARISON) t2
					on
					t1.YLJGYQDM = t2.yljgyqdm
				  WHERE   TRIM(t1.KLX) in ('0','1','2','3','4','9') and
                                      t1.XGBZ = '1' --and t1.bbh = 'v1.2'

					AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
				 GROUP BY t2.FJ_YLJGYQDM, TO_CHAR(t1.SHSJ, 'YYYYMMDD')) A ;


-- 卡类型符合率  -- BGRQ KLX
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30353' GZBH ,
    JYJLSH,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM
	    DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM
	    DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE  (b.KLX IN ('0','1','2','3','4','9') )
            AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY TO_CHAR(b.SHSJ, 'YYYYMMDD'),
        d.FJ_YLJGYQDM
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;



-- 病人性别符合率  -- BGRQ BRXB
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30354' GZBH ,
    JYJLSH,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM
        DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM
	    DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE  (b.BRXB IN ('1','2','3') )
            AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;



-- 申请医护人员ID符合率  -- BGRQ SQYHRYID
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT A.YWRQ YWRQ,
			 A.JGDM JGDM,
			 '30355' GZBH,
			 JYJLSH,
			 COALESCE((B.FHGZSH), 0) FHGZSH ,
			 'v1.2' 					bbh,
			 CURRENT_TIMESTAMP
	FROM (SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
								t2.FJ_YLJGYQDM JGDM,
								COUNT(1) JYJLSH
					 FROM DWD.TB_GENETIC_REPORT t1
					 left join
					 (SELECT yljgyqdm,FJ_YLJGYQDM FROM prefix_dq.T_DIC_YLJGDM_COMPARISON) t2
					 on
					 t1.YLJGYQDM = t2.yljgyqdm
					WHERE
					        TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
							and t1.XGBZ='1' --and t1.bbh = 'v1.2'
 
					GROUP BY t2.FJ_YLJGYQDM, TO_CHAR(t1.SHSJ, 'YYYYMMDD')) A
			LEFT JOIN
				(SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
								t2.FJ_YLJGYQDM JGDM,
								COALESCE(COUNT(1), 0) FHGZSH
					 FROM DWD.TB_GENETIC_REPORT B
					 left join
					 (SELECT yljgyqdm,FJ_YLJGYQDM FROM prefix_dq.T_DIC_YLJGDM_COMPARISON) t2
					 on
					 B.YLJGYQDM = t2.yljgyqdm
					WHERE (EXISTS
								 (SELECT 1
										FROM DWD.TB_DIC_PRACTITIONER X
										left join prefix_dq.T_DIC_YLJGDM_COMPARISON C
										on X.yljgyqdm=C.yljgyqdm
									 WHERE B.SQYHRYID = X.YHRYID and X.XGBZ='1'
										 AND t2.FJ_YLJGYQDM = C.FJ_YLJGYQDM) AND (B.SQYHRYID IS NOT NULL))
						    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                            AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'
							and B.XGBZ='1' --and B.bbh = 'v1.2'
 
					GROUP BY t2.FJ_YLJGYQDM, TO_CHAR(b.SHSJ, 'YYYYMMDD')) B
				ON(A.JGDM = B.JGDM AND A.YWRQ = B.YWRQ)    ;


-- 审核医护人员ID符合率  -- BGRQ SHYHRYID
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT A.YWRQ YWRQ,
			 A.JGDM JGDM,
			 '30357' GZBH,
			 JYJLSH,
			 COALESCE((B.FHGZSH), 0) FHGZSH ,
			 'v1.2' 					bbh,
			 CURRENT_TIMESTAMP
	FROM (SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
								t2.FJ_YLJGYQDM JGDM,
								COUNT(1) JYJLSH
					 FROM DWD.TB_GENETIC_REPORT t1
					 left join
					 (SELECT yljgyqdm,FJ_YLJGYQDM FROM prefix_dq.T_DIC_YLJGDM_COMPARISON) t2
					 on
					 t1.YLJGYQDM = t2.yljgyqdm
					WHERE
					        TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
							and t1.XGBZ='1' --and t1.bbh = 'v1.2'
 
					GROUP BY t2.FJ_YLJGYQDM, TO_CHAR(t1.SHSJ, 'YYYYMMDD')) A
			LEFT JOIN
				(SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
								t2.FJ_YLJGYQDM JGDM,
								COALESCE(COUNT(1), 0) FHGZSH
					 FROM DWD.TB_GENETIC_REPORT B
					 left join
					 (SELECT yljgyqdm,FJ_YLJGYQDM FROM prefix_dq.T_DIC_YLJGDM_COMPARISON) t2
					 on
					 B.YLJGYQDM = t2.yljgyqdm
					WHERE (EXISTS
								 (SELECT 1
										FROM DWD.TB_DIC_PRACTITIONER X
										left join prefix_dq.T_DIC_YLJGDM_COMPARISON C
										on X.yljgyqdm=C.yljgyqdm
									 WHERE B.SHYHRYID = X.YHRYID and X.XGBZ='1'
										 AND t2.FJ_YLJGYQDM = C.FJ_YLJGYQDM) AND (B.SHYHRYID IS NOT NULL))
						    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                            AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'
							and B.XGBZ='1' --and B.bbh = 'v1.2'
 
					GROUP BY t2.FJ_YLJGYQDM, TO_CHAR(b.SHSJ, 'YYYYMMDD')) B
				ON(A.JGDM = B.JGDM AND A.YWRQ = B.YWRQ)    ;


-- 申请科室编码符合率  -- BGRQ SQKS
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30358' GZBH,
    JYJLSH,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE
        (b.SQKS IN (SELECT DISTINCT YYKSDM FROM DWD.TB_DIC_DEPARTMENT ) AND b.SQKS IS NOT NULL )
        AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;




-- 报告时间符合率  -- BGRQ BGSJ
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30359' GZBH,
    JYJLSH,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE ( b.BGSJ IS NOT NULL )
        AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;



-- 申请时间符合率  -- BGRQ SQSJ
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30360' GZBH,
    JYJLSH,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE ( b.SQSJ IS NOT NULL )
        AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;



-- 采集时间符合率  -- BGRQ CJSJ
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30361' GZBH,
    JYJLSH,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE  ( b.CJSJ IS NOT NULL )
        AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;



-- 检测时间符合率  -- BGRQ JCSJ
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30362' GZBH,
    JYJLSH,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE ( b.JCSJ IS NOT NULL )
        AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;



-- 基因标本代码符合率  -- BGRQ JYBBDM
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30363' GZBH,
    JYJLSH,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE (b.JYBBDM IN ('1','2','3','4','9999') )
        AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;



-- 检测项目代码符合率  -- BGRQ JCXMDM
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30364' GZBH,
    JYJLSH,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE  (b.JCXMDM IN ('1','2','3','4','5','6','7','8','9999') )
        AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;



-- 是否存在基因变异点符合率  -- BGRQ SFBY
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30366' GZBH,
    JYJLSH,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE  (b.SFBY IN ('1','2') )
        AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;



--30405-30420




-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30405 卡号符合率
-- YZXDSJ KH
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT
		YWRQ,
		JGDM,
		'30405' GZBH,
		JYJLSH,
		COALESCE(FHGZSH, 0) FHGZSH,
		'v1.2' 					bbh ,
		CURRENT_TIMESTAMP as xgshj
	FROM (SELECT
	             TO_CHAR(t1.SHSJ,'YYYYMMDD') YWRQ,
				 t2.FJ_YLJGYQDM JGDM,
				 COUNT(*) JYJLSH,
				 SUM(CASE
			            WHEN TRIM(t1.KLX) = '0' AND t1.KH ~ '^[0-9A-Z]{9}$' THEN 1
			            WHEN TRIM(t1.KLX) = '1' AND t1.KH ~ '^[0-9]{10,10}$' THEN 1
			            WHEN TRIM(t1.KLX) = '2' AND t1.KH ~ '^[0-9]{15,15}$' THEN 1
			            WHEN TRIM(t1.KLX) = '3' AND t1.KH IS NOT NULL THEN 1
			            WHEN TRIM(t1.KLX) = '4' AND t1.KH IS NOT NULL THEN 1
			            WHEN TRIM(t1.KLX) = '9' THEN 1
			            ELSE 0
            END
        ) AS FHGZSH
					FROM DWD.TB_CIS_DRADVICE_DETAIL t1
					left join
					(SELECT yljgyqdm,FJ_YLJGYQDM FROM prefix_dq.T_DIC_YLJGDM_COMPARISON) t2
					on
					t1.YLJGYQDM = t2.yljgyqdm
				  WHERE   TRIM(t1.KLX) in ('0','1','2','3','4','9') and
                                      t1.XGBZ = '1' --and t1.bbh = 'v1.2'
 
					AND TO_CHAR(t1.SHSJ,'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(t1.SHSJ,'YYYYMMDD') < '${ETIME}'
				 GROUP BY t2.FJ_YLJGYQDM, TO_CHAR(t1.SHSJ,'YYYYMMDD')) A ;

-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30406 卡类型符合率
-- YZXDSJ KLX
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ YWRQ,
    a.JGDM AS JGDM,
    '30406' AS GZBH,
    JYJLSH AS JYJLSH,
    COALESCE(B.FHGZSH, 0) AS FHGZSH,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COUNT(1) JYJLSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
        WHERE XGBZ = '1' --and t1.bbh = 'v1.2'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COALESCE(COUNT(1), 0) FHGZSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL T1
            left join prefix_dq.T_DIC_YLJGDM_COMPARISON t2 on t1.yljgyqdm = t2.yljgyqdm
        WHERE (
                (KLX IN ('0', '1', '2', '3', '4', '9'))
                AND XGBZ = '1' --and t1.bbh = 'v1.2'
            )
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ;

-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30407 下达科室编码符合率
-- YZXDSJ XDKSBM
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ YWRQ,
    a.JGDM AS JGDM,
    '30407' AS GZBH,
    JYJLSH AS JYJLSH,
    COALESCE(B.FHGZSH, 0) AS FHGZSH,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') AS YWRQ,
            t2.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE XGBZ = '1' --and t1.bbh = 'v1.2'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') AS YWRQ,
            t2.FJ_YLJGYQDM AS JGDM,
            COALESCE(COUNT(1), 0) AS FHGZSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (
                t1.XDKSBM IN (
                    SELECT DISTINCT YYKSDM
                    FROM DWD.TB_DIC_DEPARTMENT
                )
                AND t1.XDKSBM IS NOT NULL
            )
            AND XGBZ = '1' --and t1.bbh = 'v1.2'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ;





-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30408 医嘱下达医护人员ID符合率
-- SHSJ YZXDYHRYID

insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT A.YWRQ YWRQ,
			 A.JGDM JGDM,
			 '30408' GZBH,
			 JYJLSH,
			 COALESCE((B.FHGZSH), 0) FHGZSH ,
			 'v1.2' 					bbh,
			 CURRENT_TIMESTAMP
	FROM (SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
								t2.FJ_YLJGYQDM JGDM,
								COUNT(1) JYJLSH
					 FROM DWD.TB_CIS_DRADVICE_DETAIL t1
					 left join
					 (SELECT yljgyqdm,FJ_YLJGYQDM FROM prefix_dq.T_DIC_YLJGDM_COMPARISON) t2
					 on
					 t1.YLJGYQDM = t2.yljgyqdm
					WHERE
					        TO_CHAR(t1.SHSJ,'YYYYMMDD') >= '${BTIME}'
                            AND TO_CHAR(t1.SHSJ,'YYYYMMDD') < '${ETIME}'
							and t1.XGBZ='1' --and t1.bbh = 'v1.2'
 
					GROUP BY t2.FJ_YLJGYQDM, TO_CHAR(t1.SHSJ, 'YYYYMMDD')) A
			LEFT JOIN
				(SELECT TO_CHAR(B.SHSJ, 'YYYYMMDD') YWRQ,
								t2.FJ_YLJGYQDM JGDM,
								COALESCE(COUNT(1), 0) FHGZSH
					 FROM DWD.TB_CIS_DRADVICE_DETAIL B
					 left join
					 (SELECT yljgyqdm,FJ_YLJGYQDM FROM prefix_dq.T_DIC_YLJGDM_COMPARISON) t2
					 on
					 B.YLJGYQDM = t2.yljgyqdm
					WHERE (EXISTS
								 (SELECT 1
										FROM DWD.TB_DIC_PRACTITIONER X
										left join prefix_dq.T_DIC_YLJGDM_COMPARISON C
										on X.yljgyqdm=C.yljgyqdm
									 WHERE B.YZXDYHRYID = X.YHRYID and X.XGBZ='1'
										 AND t2.FJ_YLJGYQDM = C.FJ_YLJGYQDM) AND (B.YZXDYHRYID IS NOT NULL))
						    AND TO_CHAR(B.SHSJ,'YYYYMMDD') >= '${BTIME}'
                            AND TO_CHAR(B.SHSJ,'YYYYMMDD') < '${ETIME}'
							and B.XGBZ='1' --and B.bbh = 'v1.2'
 
					GROUP BY t2.FJ_YLJGYQDM, TO_CHAR(B.SHSJ, 'YYYYMMDD')) B
				ON(A.JGDM = B.JGDM AND A.YWRQ = B.YWRQ)    ;

-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30409 医嘱下达时间符合率
-- YZXDSJ YZXDSJ
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ YWRQ,
    a.JGDM AS JGDM,
    '30409' AS GZBH,
    JYJLSH AS JYJLSH,
    COALESCE(B.FHGZSH, 0) AS FHGZSH,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COUNT(1) JYJLSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE XGBZ = '1' --and t1.bbh = 'v1.2'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COALESCE(COUNT(1), 0) FHGZSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (
                (YZXDSJ IS NOT NULL)
                AND XGBZ = '1' --and t1.bbh = 'v1.2'
            )
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ;



-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30410 执行科室编码符合率
-- YZXDSJ ZXKSBM
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ YWRQ,
    a.JGDM AS JGDM,
    '30410' AS GZBH,
    JYJLSH AS JYJLSH,
    COALESCE(B.FHGZSH, 0) AS FHGZSH,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') AS YWRQ,
            t2.FJ_YLJGYQDM AS JGDM,
            COUNT(1) AS JYJLSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE XGBZ = '1' --and t1.bbh = 'v1.2'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') AS YWRQ,
            t2.FJ_YLJGYQDM AS JGDM,
            COALESCE(COUNT(1), 0) AS FHGZSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
            LEFT JOIN (
                SELECT DISTINCT YYKSDM
                FROM DWD.TB_DIC_DEPARTMENT
            ) A ON t1.ZXKSBM = A.YYKSDM
        WHERE ZXKSBM IS NOT NULL
            AND XGBZ = '1' --and t1.bbh = 'v1.2'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ;
-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30411 医嘱执行医护人员ID符合率
-- SHSJ YZZXYHRYID
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT A.YWRQ YWRQ,
			 A.JGDM JGDM,
			 '30411' GZBH,
			 JYJLSH,
			 COALESCE((B.FHGZSH), 0) FHGZSH ,
			 'v1.2' 					bbh,
			 CURRENT_TIMESTAMP
	FROM (SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
								t2.FJ_YLJGYQDM JGDM,
								COUNT(1) JYJLSH
					 FROM DWD.TB_CIS_DRADVICE_DETAIL t1
					 left join
					 (SELECT yljgyqdm,FJ_YLJGYQDM FROM prefix_dq.T_DIC_YLJGDM_COMPARISON) t2
					 on
					 t1.YLJGYQDM = t2.yljgyqdm
					WHERE
					        TO_CHAR(t1.SHSJ,'YYYYMMDD') >= '${BTIME}'
                            AND TO_CHAR(t1.SHSJ,'YYYYMMDD') < '${ETIME}'
							and t1.XGBZ='1' --and t1.bbh = 'v1.2'
 
					GROUP BY t2.FJ_YLJGYQDM, TO_CHAR(t1.SHSJ, 'YYYYMMDD')) A
			LEFT JOIN
				(SELECT TO_CHAR(B.SHSJ, 'YYYYMMDD') YWRQ,
								t2.FJ_YLJGYQDM JGDM,
								COALESCE(COUNT(1), 0) FHGZSH
					 FROM DWD.TB_CIS_DRADVICE_DETAIL B
					 left join
					 (SELECT yljgyqdm,FJ_YLJGYQDM FROM prefix_dq.T_DIC_YLJGDM_COMPARISON) t2
					 on
					 B.YLJGYQDM = t2.yljgyqdm
					WHERE (EXISTS
								 (SELECT 1
										FROM DWD.TB_DIC_PRACTITIONER X
										left join prefix_dq.T_DIC_YLJGDM_COMPARISON C
										on X.yljgyqdm=C.yljgyqdm
									 WHERE B.YZZXYHRYID = X.YHRYID and X.XGBZ='1'
										 AND t2.FJ_YLJGYQDM = C.FJ_YLJGYQDM) AND (B.YZZXYHRYID IS NOT NULL))
						    AND TO_CHAR(B.SHSJ,'YYYYMMDD') >= '${BTIME}'
                            AND TO_CHAR(B.SHSJ,'YYYYMMDD') < '${ETIME}'
							and B.XGBZ='1' --and B.bbh = 'v1.2'
 
					GROUP BY t2.FJ_YLJGYQDM, TO_CHAR(B.SHSJ, 'YYYYMMDD')) B
				ON(A.JGDM = B.JGDM AND A.YWRQ = B.YWRQ)    ;


-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30412 医嘱执行时间符合率
-- YZXDSJ YZZXSJ
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30412' AS GZBH,
    JYJLSH AS JYJLSH,
    COALESCE(B.FHGZSH, 0) AS FHGZSH,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COUNT(1) JYJLSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE XGBZ = '1' --and t1.bbh = 'v1.2'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COALESCE(COUNT(1), 0) FHGZSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (
                TO_CHAR(t1.YZZXSJ, 'YYYYMMDD') IS NOT NULL
                AND XGBZ = '1' --and t1.bbh = 'v1.2'
            )
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ;

-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30416 医嘱项目类型符合率
-- YZXDSJ YZLX


insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30416' AS GZBH,
    JYJLSH AS JYJLSH,
    COALESCE(B.FHGZSH, 0) AS FHGZSH,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COUNT(1) JYJLSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE XGBZ = '1' --and t1.bbh = 'v1.2'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COALESCE(COUNT(1), 0) FHGZSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (
                (
                    YZLX IN (
                        '01',
                        '02',
                        '03',
                        '04',
                        '05',
                        '06',
                        '07',
                        '08',
                        '99'
                    )
                )
                AND XGBZ = '1' --and t1.bbh = 'v1.2'
            )
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ;

-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30417 用药途径代码符合率
-- YZXDSJ YF
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30417' AS GZBH,
    JYJLSH AS JYJLSH,
    COALESCE(B.FHGZSH, 0) AS FHGZSH,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COUNT(1) JYJLSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE XGBZ = '1' --and t1.bbh = 'v1.2'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COALESCE(COUNT(1), 0) FHGZSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (
                (
                    (
                        YZLX = '01'
                        AND (
                            YF IN (
                                '1',
                                '2',
                                '3',
                                '4',
                                '401',
                                '402',
                                '403',
                                '404',
                                '405',
                                '5',
                                '6',
                                '601',
                                '602',
                                '603',
                                '604',
                                '605',
                                '606',
                                '607',
                                '608',
                                '609',
                                '610',
                                '611',
                                '612',
                                '699',
                                '7',
                                '9'
                            )
                        )
                    )
                    or (
                        YZLX <> '01'
                        and YF = '-'
                    )
                )
                AND XGBZ = '1' --and t1.bbh = 'v1.2'
            )
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ;

-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30419 用药频次代码符合率
-- YZXDSJ YYPCDM
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30419' AS GZBH,
    JYJLSH AS JYJLSH,
    COALESCE(B.FHGZSH, 0) AS FHGZSH,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COUNT(1) JYJLSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE XGBZ = '1' --and t1.bbh = 'v1.2'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COALESCE(COUNT(1), 0) FHGZSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (
                (
                    (
                        YZLX = '01'
                        AND (
                            YYPCDM IN (
                                'QD',
                                'BID',
                                'TID',
                                'QID',
                                'Q30D',
                                'QW',
                                'Q2W',
                                'BIW',
                                'TIW',
                                'Q30M',
                                'Q1H',
                                'Q2H',
                                'Q3H',
                                'Q4H',
                                'Q5H',
                                'Q6H',
                                'Q8H',
                                'Q12H',
                                'Q72H',
                                'QM',
                                'QN',
                                'QON',
                                'ST',
                                'QOD',
                                'Q5D',
                                'Q10D',
                                'C12H',
                                'C24H',
                                'PRN',
                                'AC',
                                'AM'
                            )
                        )
                    )
                    or (
                        YZLX <> '01'
                        and YYPCDM = '-'
                    )
                )
                AND XGBZ = '1' --and t1.bbh = 'v1.2'
            )
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ;
-- 住院医嘱明细表 (TB_CIS_DRADVICE_DETAIL)
-- 30420 皮试判别符合率
-- YZXDSJ SFPS
insert into prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq, jgdm, gzbh, jyjlsh, fhgzsh, BBH, xgshj)
SELECT a.YWRQ AS YWRQ,
    a.JGDM AS JGDM,
    '30420' AS GZBH,
    JYJLSH AS JYJLSH,
    COALESCE(B.FHGZSH, 0) AS FHGZSH,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COUNT(1) JYJLSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE XGBZ = '1' --and t1.bbh = 'v1.2'
            and t1.SFPS IS not NULL
            and t1.SFPS != ''
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) A
    LEFT JOIN (
        SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
            t2.FJ_YLJGYQDM JGDM,
            COALESCE(COUNT(1), 0) FHGZSH
        FROM DWD.TB_CIS_DRADVICE_DETAIL t1
            LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (
                SFPS IN ('0', '1')
                AND XGBZ = '1' --and t1.bbh = 'v1.2'
                and SFPS IS not NULL
                and SFPS != ''
            )
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
            AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,
            TO_CHAR(t1.SHSJ, 'YYYYMMDD')
    ) B ON A.JGDM = B.JGDM
    AND A.YWRQ = B.YWRQ