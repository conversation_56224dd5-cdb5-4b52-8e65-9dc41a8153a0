--40035 主检报告记录上传及时性update

update dwd_tj.T_AUDIT_FINAL_STATUS
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40035'
WHERE round(date_part('epoch',JLGXSJ) - date_part('epoch',ZSSJ) / 86400, 2) > 1
    and xgbz = '1'
    and to_char(ZSSJ,'YYYYMMDD') >= '${BTIME}'
    and to_char(ZSSJ,'YYYYMMDD') < '${ETIME}'
    and (war_note not like '%40035%' or war_note is null)
    and send_flag='0';

