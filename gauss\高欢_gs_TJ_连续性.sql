--T_AUDIT_FINAL_STATUS-连续性 C044
--测量方法  根据业务日期统计当日上传表单，有表单上传填报1（根据业务日期计算上传日数，总上传日数为统计时间范围的天数）


INSERT INTO prefix_dq_tj.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywrq,
  b.fj_yljgdm,
  'C044',
  coalesce(sum(a.sjl),0) as JYJLSH,
  case when coalesce(sum(a.sjl),0)=0 then 0 else 1 END as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP as xgshj
from prefix_dq_tj.tb_sjlmx_ywsj_tj a
RIGHT JOIN prefix_dq_tj.t_dic_fjyljgdm_posid b
	ON 'posid_'||substr(a.dbname from 3 for 5) = b.posid and a.tbname='T_AUDIT_FINAL_STATUS' 
	AND a.ywrq >= '${BTIME}' AND a.ywrq < '${ETIME}'
group by a.ywrq,b.fj_yljgdm