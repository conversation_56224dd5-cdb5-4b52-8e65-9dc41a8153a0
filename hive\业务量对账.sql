insert overwrite table hive_wsjk_v12_prod_source_ods.tb_ods_ywltj_lzh
SELECT  
    c.ywsj,
    d.fj_yljgyqdm jgdm,
    c.tbname,
    sum(c.sjl),
    c.posid,
    case when sum(c.sjl) != '0' then 1 else 0 end as bz,
    c.jlgxsj
from 
    (select * 
        from(
            select(ywsj,jgdm,tbname,sjl,posid,jlgxsj,row_number() over(partition by ywsj,jgdm,tbname order by jlgxsj desc) as rn
            from(
                select 
                    cast(from_unixtime(unix_timestamp(ywsj,'yyyyMMdd')) as timestamp) ywsj,
                    yljgyqdm as jgdm,
                    'tb_his_mz_reg' as tbname,
                    tb_his_mz_reg as sjl,
                    posid,
                    jlgxsj
                from hive_wsjk_v12_prod_source_ods.tb_uploads_stat
            union all
                select 
                    cast(from_unixtime(unix_timestamp(ywsj,'yyyyMMdd')) as timestamp) ywsj,
                    yljgyqdm as jgdm,
                    'tb_his_mz_reg' as tbname,
                    tb_his_mz_reg as sjl,
                    posid,
                    jlgxsj
                from hive_wsjk_v12_prod_source_ods.tb_uploads_stat
            ) a
            ) a where rn = 1 ) c
        inner join 
        hive_wsjk_v12_dev_source_ODS.t_dic_yljgdm_comparison d
            on c.jgdm = d.yljgyqdm
        group by c.ywsj,d.fj_yljgyqdm,c.tbname,c.posid,c.jlgxsj
--开窗函数去掉无效记录，并将记录上传日期对应的业务日期添加进来
with lzh as (
    select * from
    (
        SELECT
            ywsj,
            jgdm,
            tbname,
            sjl,
            posid,
            bz,
            jlgxsj,
            row_number() over(partition by ywsj,jgdm,tbname order by jlgxsj desc) as rn
        FROM hive_wsjk_v12_prod_source_ods.tb_ods_ywltj_lzh
    ) b
    where rn = 1  ),
jg_ywsj as (
    select 
        a.datelist,
        b.fj_yljgdm,
        b.posid
    from hive_wsjk_v12_prod_source_ods.jg_ywrq a
    cross join hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid B
    where a.datelist >= '${BTIME}' and a.datelist < '${ETIME}'
)
insert overwrite table hive_wsjk_v12_prod_source_ods.tb_ods_ywltj_detail
SELECT
    a.datalist,
    a.fj_yljgdm,
    b.tbname,
    b.sjl,
    a.posid,
    b.bz,
    b.jlgxsj,
    b.rn
from jg_ywsj a
left join lzh b
    on a.datelist = regexp_replace(substr(b.ywsj,1,10),'-','') and a.fj_yljgdm = b.jgdm;



