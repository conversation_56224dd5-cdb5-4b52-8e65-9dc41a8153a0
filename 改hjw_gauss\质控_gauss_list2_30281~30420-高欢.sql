insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30281' AS GZBH,
    CWZ --'TB_BA_SYJBK' AS BM,
    --'RYRQ' AS ZDM,
,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    b.RYRQ AS CWZ,
                    COUNT(1) AS CWL
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND b.RYRQ !~ '^\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{2}:\\d{2}:\\d{2}$'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.RYRQ
            ) a
    ) b
WHERE rn <= 10;


--入院确诊日期符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30282' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'QZRQ' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.QZRQ AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND b.QZRQ IS NOT NULL
                    AND b.QZRQ != ''
                    AND (
                        CASE
                            WHEN b.QZRQ ~ '^\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])$' THEN 1
                            ELSE 0
                        END
                    ) = 0
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.QZRQ
            ) a
    ) b
WHERE rn <= 10;
--入院科室符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30283' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'RYKS' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    b.RYKS AS CWZ,
                    COUNT(1) AS CWL
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND b.RYKS NOT IN (
                        '01',
                        '02',
                        '03',
                        '0301',
                        '0302',
                        '0303',
                        '0304',
                        '030401',
                        '030402',
                        '0305',
                        '0306',
                        '0307',
                        '030701',
                        '030702',
                        '0308',
                        '030801',
                        '030802',
                        '030803',
                        '030804',
                        '0309',
                        '0310',
                        '0311',
                        '031102',
                        '031101',
                        '04',
                        '0401',
                        '040101',
                        '040102',
                        '040103',
                        '040104',
                        '040105',
                        '040106',
                        '040107',
                        '04010701',
                        '040108',
                        '040109',
                        '0402',
                        '0403',
                        '040301',
                        '040302',
                        '04030201',
                        '040303',
                        '0404',
                        '040401',
                        '040402',
                        '0405',
                        '040501',
                        '0406',
                        '040601',
                        '0407',
                        '0408',
                        '0409',
                        '040901',
                        '05',
                        '0501',
                        '0502',
                        '0503',
                        '0504',
                        '0505',
                        '0506',
                        '06',
                        '0601',
                        '0602',
                        '0603',
                        '0604',
                        '0605',
                        '0606',
                        '07',
                        '0701',
                        '0702',
                        '0703',
                        '070301',
                        '0704',
                        '0705',
                        '0706',
                        '0707',
                        '0708',
                        '0709',
                        '0710',
                        '0711',
                        '071101',
                        '0712',
                        '08',
                        '0801',
                        '0802',
                        '0803',
                        '0804',
                        '0805',
                        '0806',
                        '080601',
                        '09',
                        '0901',
                        '0902',
                        '0903',
                        '0904',
                        '0905',
                        '0906',
                        '10',
                        '11',
                        '1101',
                        '1102',
                        '1103',
                        '1104',
                        '12',
                        '1201',
                        '1202',
                        '1203',
                        '1204',
                        '1205',
                        '120501',
                        '120502',
                        '120503',
                        '1206',
                        '1207',
                        '1208',
                        '1209',
                        '1210',
                        '1211',
                        '1212',
                        '1213',
                        '13',
                        '1301',
                        '1302',
                        '1303',
                        '14',
                        '1401',
                        '15',
                        '1501',
                        '150101',
                        '150102',
                        '150103',
                        '1502',
                        '1503',
                        '1504',
                        '1505',
                        '1506',
                        '1507',
                        '1508',
                        '16',
                        '1601',
                        '1602',
                        '1603',
                        '1604',
                        '1605',
                        '1606',
                        '1607',
                        '17',
                        '18',
                        '19',
                        '1901',
                        '190101',
                        '1902',
                        '1903',
                        '20',
                        '2001',
                        '200101',
                        '2002',
                        '200201',
                        '200202',
                        '200203',
                        '200204',
                        '200205',
                        '200206',
                        '2003',
                        '200301',
                        '200302',
                        '2004',
                        '2005',
                        '2006',
                        '2007',
                        '200701',
                        '200702',
                        '200703',
                        '2008',
                        '2009',
                        '2010',
                        '21',
                        '22',
                        '23',
                        '2301',
                        '2302',
                        '2303',
                        '2304',
                        '2305',
                        '2306',
                        '24',
                        '25',
                        '26',
                        '27',
                        '28',
                        '30',
                        '3001',
                        '3002',
                        '3003',
                        '3004',
                        '3005',
                        '3006',
                        '31',
                        '32',
                        '3201',
                        '3202',
                        '320201',
                        '3203',
                        '3204',
                        '3205',
                        '3206',
                        '3207',
                        '3208',
                        '3209',
                        '320901',
                        '320902',
                        '3210',
                        '321001',
                        '32100101',
                        '3211',
                        '50',
                        '5001',
                        '500101',
                        '500102',
                        '500103',
                        '500104',
                        '500105',
                        '500106',
                        '500107',
                        '500108',
                        '500109',
                        '500110',
                        '5002',
                        '500201',
                        '500202',
                        '500203',
                        '500204',
                        '5003',
                        '500301',
                        '500302',
                        '5004',
                        '5005',
                        '5006',
                        '5007',
                        '5008',
                        '5009',
                        '5010',
                        '501001',
                        '501002',
                        '5011',
                        '5012',
                        '5013',
                        '5014',
                        '5015',
                        '501501',
                        '501502',
                        '5016',
                        '501601',
                        '5017',
                        '5018',
                        '501801',
                        '501802',
                        '501803',
                        '501804',
                        '501805',
                        '501806',
                        '501807',
                        '501808',
                        '501809',
                        '501810',
                        '501811',
                        '501812',
                        '51',
                        '5101',
                        '5102',
                        '5103',
                        '5104',
                        '5105',
                        '5106',
                        '52',
                        '69',
                        '6901'
                    )
                    OR b.RYKS IS NULL
                    OR b.RYKS = ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.RYKS
            ) a
    ) b
WHERE rn <= 10;
--出院日期符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30285' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'CYRQ' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.CYRQ AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.CYRQ ~ '^\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{2}:\\d{2}:\\d{2}$' THEN 1
                            ELSE 0
                        END
                    ) = 0
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.CYRQ
            ) a
    ) b
WHERE rn <= 10;
--出院科室符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30286' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'CYKS' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.CYKS AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.CYKS IN (
                                '01',
                                '02',
                                '03',
                                '0301',
                                '0302',
                                '0303',
                                '0304',
                                '030401',
                                '030402',
                                '0305',
                                '0306',
                                '0307',
                                '030701',
                                '030702',
                                '0308',
                                '030801',
                                '030802',
                                '030803',
                                '030804',
                                '0309',
                                '0310',
                                '0311',
                                '031102',
                                '031101',
                                '04',
                                '0401',
                                '040101',
                                '040102',
                                '040103',
                                '040104',
                                '040105',
                                '040106',
                                '040107',
                                '04010701',
                                '040108',
                                '040109',
                                '0402',
                                '0403',
                                '040301',
                                '040302',
                                '04030201',
                                '040303',
                                '0404',
                                '040401',
                                '040402',
                                '0405',
                                '040501',
                                '0406',
                                '040601',
                                '0407',
                                '0408',
                                '0409',
                                '040901',
                                '05',
                                '0501',
                                '0502',
                                '0503',
                                '0504',
                                '0505',
                                '0506',
                                '06',
                                '0601',
                                '0602',
                                '0603',
                                '0604',
                                '0605',
                                '0606',
                                '07',
                                '0701',
                                '0702',
                                '0703',
                                '070301',
                                '0704',
                                '0705',
                                '0706',
                                '0707',
                                '0708',
                                '0709',
                                '0710',
                                '0711',
                                '071101',
                                '0712',
                                '08',
                                '0801',
                                '0802',
                                '0803',
                                '0804',
                                '0805',
                                '0806',
                                '080601',
                                '09',
                                '0901',
                                '0902',
                                '0903',
                                '0904',
                                '0905',
                                '0906',
                                '10',
                                '11',
                                '1101',
                                '1102',
                                '1103',
                                '1104',
                                '12',
                                '1201',
                                '1202',
                                '1203',
                                '1204',
                                '1205',
                                '120501',
                                '120502',
                                '120503',
                                '1206',
                                '1207',
                                '1208',
                                '1209',
                                '1210',
                                '1211',
                                '1212',
                                '1213',
                                '13',
                                '1301',
                                '1302',
                                '1303',
                                '14',
                                '1401',
                                '15',
                                '1501',
                                '150101',
                                '150102',
                                '150103',
                                '1502',
                                '1503',
                                '1504',
                                '1505',
                                '1506',
                                '1507',
                                '1508',
                                '16',
                                '1601',
                                '1602',
                                '1603',
                                '1604',
                                '1605',
                                '1606',
                                '1607',
                                '17',
                                '18',
                                '19',
                                '1901',
                                '190101',
                                '1902',
                                '1903',
                                '20',
                                '2001',
                                '200101',
                                '2002',
                                '200201',
                                '200202',
                                '200203',
                                '200204',
                                '200205',
                                '200206',
                                '2003',
                                '200301',
                                '200302',
                                '2004',
                                '2005',
                                '2006',
                                '2007',
                                '200701',
                                '200702',
                                '200703',
                                '2008',
                                '2009',
                                '2010',
                                '21',
                                '22',
                                '23',
                                '2301',
                                '2302',
                                '2303',
                                '2304',
                                '2305',
                                '2306',
                                '24',
                                '25',
                                '26',
                                '27',
                                '28',
                                '30',
                                '3001',
                                '3002',
                                '3003',
                                '3004',
                                '3005',
                                '3006',
                                '31',
                                '32',
                                '3201',
                                '3202',
                                '320201',
                                '3203',
                                '3204',
                                '3205',
                                '3206',
                                '3207',
                                '3208',
                                '3209',
                                '320901',
                                '320902',
                                '3210',
                                '321001',
                                '32100101',
                                '3211',
                                '50',
                                '5001',
                                '500101',
                                '500102',
                                '500103',
                                '500104',
                                '500105',
                                '500106',
                                '500107',
                                '500108',
                                '500109',
                                '500110',
                                '5002',
                                '500201',
                                '500202',
                                '500203',
                                '500204',
                                '5003',
                                '500301',
                                '500302',
                                '5004',
                                '5005',
                                '5006',
                                '5007',
                                '5008',
                                '5009',
                                '5010',
                                '501001',
                                '501002',
                                '5011',
                                '5012',
                                '5013',
                                '5014',
                                '5015',
                                '501501',
                                '501502',
                                '5016',
                                '501601',
                                '5017',
                                '5018',
                                '501801',
                                '501802',
                                '501803',
                                '501804',
                                '501805',
                                '501806',
                                '501807',
                                '501808',
                                '501809',
                                '501810',
                                '501811',
                                '501812',
                                '51',
                                '5101',
                                '5102',
                                '5103',
                                '5104',
                                '5105',
                                '5106',
                                '52',
                                '69',
                                '6901'
                            ) THEN 1
                            ELSE 0
                        END
                    ) = 0
                    AND b.CYKS IS NOT NULL
                    AND b.CYKS != ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.CYKS
            ) a
    ) b
WHERE rn <= 10;
--中医闿怿诊诊断符合率
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30287' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'MZZD_ZY' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.MZZD_ZY AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.MZZD_ZY ~ '\\d+(\.\d+)*$' THEN 1
                            ELSE 0
                        END
                    ) = 0
                    OR b.MZZD_ZY IS NULL
                    OR b.MZZD_ZY = ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.MZZD_ZY
            ) a
    ) b
WHERE rn <= 10;
--实施临床路径符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30290' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'SSLCLJ' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.SSLCLJ AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.SSLCLJ IN ('1', '2', '3') THEN 1
                            ELSE 0
                        END
                    ) = 0
                    AND b.SSLCLJ IS NOT NULL
                    AND b.SSLCLJ != ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.SSLCLJ
            ) a
    ) b
WHERE rn <= 10;
--自制中医制剂符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30291' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'ZZZYZJ' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.ZZZYZJ AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.ZZZYZJ IN ('1', '2') THEN 1
                            ELSE 0
                        END
                    ) = 0
                    OR b.ZZZYZJ IS NULL
                    OR b.ZZZYZJ = ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.ZZZYZJ
            ) a
    ) b
WHERE rn <= 10;
--使用中医诊疗设备符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30292' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'ZYZLSB' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.ZYZLSB AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.ZYZLSB IN ('1', '2') THEN 1
                            ELSE 0
                        END
                    ) = 0
                    OR b.ZYZLSB IS NULL
                    OR b.ZYZLSB = ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.ZYZLSB
            ) a
    ) b
WHERE rn <= 10;
--使用中医诊疗技术符合率
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30293' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'ZYZLJS' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.ZYZLJS AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.ZYZLJS IN ('1', '2') THEN 1
                            ELSE 0
                        END
                    ) = 0
                    OR b.ZYZLJS IS NULL
                    OR b.ZYZLJS = ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.ZYZLJS
            ) a
    ) b
WHERE rn <= 10;
--辨证施护符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30294' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'BZSH' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.BZSH AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.BZSH IN ('1', '2') THEN 1
                            ELSE 0
                        END
                    ) = 0
                    OR b.BZSH IS NULL
                    OR b.BZSH = ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.BZSH
            ) a
    ) b
WHERE rn <= 10;
--中医主病编码符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30295' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'ZYZD_ZY' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.ZYZD_ZY AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.ZYZD_ZY ~ '\\d+(\.\d+)*$' THEN 1
                            ELSE 0
                        END
                    ) = 0
                    OR b.ZYZD_ZY IS NULL
                    OR b.ZYZD_ZY = ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.ZYZD_ZY
            ) a
    ) b
WHERE rn <= 10;
--中医主证编码符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30296' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'ZYZZ_ZY' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.ZYZZ_ZY AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.ZYZZ_ZY ~ '\\d+(\.\d+)*$' THEN 1
                            ELSE 0
                        END
                    ) = 0
                    OR b.ZYZZ_ZY IS NULL
                    OR b.ZYZZ_ZY = ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.ZYZZ_ZY
            ) a
    ) b
WHERE rn <= 10;
--中医入院病情符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30297' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'ZYRYBQ' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.ZYRYBQ AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.ZYRYBQ IN (
                                '1',
                                '2',
                                '3',
                                '4'
                            ) THEN 1
                            ELSE 0
                        END
                    ) = 0
                    OR b.ZYRYBQ IS NULL
                    OR b.ZYRYBQ = ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.ZYRYBQ
            ) a
    ) b
WHERE rn <= 10;
--中医出院情况符合玿
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30298' AS GZBH,
    --'TB_BA_SYJBK' AS BM,
    --'ZGQK_ZY' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    NOW() AS XGSHJ
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.GDRQ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COUNT(1) AS CWL,
                    b.ZGQK_ZY AS CWZ
                FROM DWD.TB_BA_SYJBK b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE b.GDRQ IS NOT NULL
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.GDRQ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    AND (
                        CASE
                            WHEN b.ZGQK_ZY IN (
                                '1',
                                '2',
                                '3',
                                '4',
                                '5'
                            ) THEN 1
                            ELSE 0
                        END
                    ) = 0
                    OR b.ZGQK_ZY IS NULL
                    OR b.ZGQK_ZY = ''
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.GDRQ, 'YYYYMMDD'),
                    b.ZGQK_ZY
            ) a
    ) b
WHERE rn <= 10;
-- 基因检测报告表 (TB_GENETIC_REPORT)
-- 30349 报告日期符合玿
-- BGRQ BGRQ
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30349' GZBH,
    --'TB_GENETIC_REPORT' BM,
    --'BGRQ' ZDM,
    CWZ,
    COALESCE((CWL), 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT b.SHSJ YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.BGRQ CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE NOT(
                        b.BGRQ ~ '^\\d{8}$'
                        AND EXTRACT(EPOCH FROM TO_TIMESTAMP(b.BGRQ, 'YYYYMMDD'))::BIGINT IS NOT NULL)
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'--AND b.bbh = 'v1.2'
                GROUP BY d.FJ_YLJGYQDM,
                    b.SHSJ,b.BGRQ
            ) a
    ) b
WHERE rn <= 10;
-- 基因检测报告表 (TB_GENETIC_REPORT)
-- 30351 门诊/住院标志符合玿
-- BGRQ MZZYBZ
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30351' AS GZBH,
    --'TB_GENETIC_REPORT' AS BM,
    --'MZZYBZ' AS ZDM,
    CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT b.SHSJ YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) AS CWL,
                    b.MZZYBZ AS CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE (
                        b.MZZYBZ not IN ('1', '2')
                    )
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                GROUP BY d.FJ_YLJGYQDM,
                    b.SHSJ,
                    b.MZZYBZ
            ) a
    ) b
WHERE rn <= 10;
-- 卡号符合玿 -- BGRQ KH
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30352' GZBH,
    --'TB_GENETIC_REPORT' BM ,
    --'KH' ZDM,
    CWZ,
    COALESCE((CWL), 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT b.SHSJ YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.KH AS CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE (
                        CASE
                            WHEN TRIM(b.KLX) = '0'
                            AND b.KH ~ '^[0-9A-Z]{9}$' THEN 1
                            WHEN TRIM(b.KLX) = '1'
                            AND b.KH ~ '^[0-9]{10,10}$' THEN 1
                            WHEN TRIM(b.KLX) = '2'
                            AND b.KH ~ '^[0-9]{15,15}$' THEN 1
                            WHEN TRIM(b.KLX) = '3'
                            AND b.KH IS NOT NULL THEN 1
                            WHEN TRIM(b.KLX) = '4'
                            AND b.KH IS NOT NULL THEN 1
                            WHEN TRIM(b.KLX) = '9' THEN 1
                            ELSE 0
                        END
                    ) = 0
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                    and TRIM(b.KLX) in ('0', '1', '2', '3', '4', '9')
                GROUP BY d.FJ_YLJGYQDM,
                    b.SHSJ,
                    b.KH
            ) a
    ) b
WHERE rn <= 10;
-- 卡类型符合率  -- BGRQ KLX
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30353' GZBH,
    --'TB_GENETIC_REPORT' BM ,
    --'KLX' ZDM,
    CWZ,
    COALESCE((CWL), 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT b.SHSJ YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.KLX CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE NOT (
                        b.KLX IN (
                            '0',
                            '1',
                            '2',
                            '3',
                            '4',
                            '9'
                        )
                    )
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}' 
                GROUP BY b.SHSJ,
                    d.FJ_YLJGYQDM,
                    b.KLX
            ) a
    ) b
WHERE rn <= 10;
-- 病人性别符合玿 -- BGRQ BRXB
insert into  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,cwz,cwl,BBH,xgshj)
SELECT
    A.YWRQ YWRQ,
    A.JGDM JGDM,
    '30354' GZBH ,
    --'TB_GENETIC_REPORT' BM ,
    --'BRXB' ZDM,
    cwz,
    COALESCE((B.FHGZSH),0) FHGZSH,
	'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COUNT(1) JYJLSH
    FROM
        DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) A
LEFT JOIN (
    SELECT
        TO_CHAR(b.SHSJ, 'YYYYMMDD')  YWRQ,
        d.FJ_YLJGYQDM AS JGDM,
        COALESCE(COUNT(1),0) FHGZSH
    FROM
	    DWD.TB_GENETIC_REPORT b
    left join
        prefix_dq.T_DIC_YLJGDM_COMPARISON d
    on
        b.yljgyqdm = d.yljgyqdm
    WHERE  (b.BRXB NOT IN ('1','2','3') )
            AND b.XGBZ = '1'
        --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
        AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${BTIME}'
        --and b.bbh = 'v1.2' 
 
    GROUP BY d.FJ_YLJGYQDM,
        TO_CHAR(b.SHSJ, 'YYYYMMDD')
) B
ON A.JGDM =B.JGDM AND A.YWRQ=B.YWRQ ;



-- 申请医护人员ID符合率  -- BGRQ SQYHRYID

-- 审核医护人员ID符合玿 -- BGRQ SHYHRYID
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30355' GZBH,
    --'TB_GENETIC_REPORT' BM ,
    --'SQYHRYID' ZDM,
    CWZ,
    COALESCE((CWL), 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.SQYHRYID CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE (
                        NOT EXISTS (
                            SELECT 1
                            FROM DWD.TB_DIC_PRACTITIONER X
                                LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON C ON X.yljgyqdm = C.yljgyqdm
                            WHERE B.SQYHRYID = X.YHRYID
                                AND X.XGBZ = '1'
                                AND d.FJ_YLJGYQDM = C.FJ_YLJGYQDM
                        )
                        AND B.SQYHRYID IS NOT NULL
                    )
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.SHSJ, 'YYYYMMDD'),
                    b.SQYHRYID
            ) a
    ) b
WHERE rn <= 10;
-- 审核医护人员ID符合玿 -- BGRQ SHYHRYID
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30357' GZBH,
    --'TB_GENETIC_REPORT' BM ,
    --'SHYHRYID' ZDM,
    CWZ,
    COALESCE(CWL, 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.SHYHRYID CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE (
                        NOT EXISTS (
                            SELECT 1
                            FROM DWD.TB_DIC_PRACTITIONER X
                                left join prefix_dq.T_DIC_YLJGDM_COMPARISON C on X.yljgyqdm = C.yljgyqdm
                            WHERE B.SHYHRYID = X.YHRYID
                                and X.XGBZ = '1'
                                AND d.FJ_YLJGYQDM = C.FJ_YLJGYQDM
                        )
                        AND (B.SHYHRYID IS NOT NULL)
                    )
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}' --AND b.bbh = 'v1.2'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.SHSJ, 'YYYYMMDD'),
                    b.SHYHRYID
            ) a
    ) b
WHERE rn <= 10;
-- 申请科室编码符合玿 -- BGRQ SQKS
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30358' GZBH,
    --'TB_GENETIC_REPORT' BM,
    --'SQKS' ZDM,
    CWZ,
    COALESCE(CWL, 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.SQKS CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE (
                        b.SQKS NOT IN (
                            SELECT DISTINCT YYKSDM
                            FROM DWD.TB_DIC_DEPARTMENT
                        )
                        or b.SQKS IS  NULL
                    )
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.SHSJ, 'YYYYMMDD'),
                    b.SQKS
            ) a
    ) b
WHERE rn <= 10;
-- 报告时间符合玿 -- BGRQ BGSJ
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30359' GZBH,
    --'TB_GENETIC_REPORT' BM,
    --'BGSJ' ZDM,
    CWZ,
    COALESCE(CWL, 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.BGSJ CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE NOT(b.BGSJ IS NOT NULL)
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.SHSJ, 'YYYYMMDD'),
                    b.BGSJ
            ) a
    ) b
WHERE rn <= 10;
-- 申请时间符合玿 -- BGRQ SQSJ
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30360' GZBH,
    --'TB_GENETIC_REPORT' BM,
    --'SQSJ' ZDM,
    CWZ,
    COALESCE((CWL), 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.SQSJ CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE NOT (b.SQSJ IS NOT NULL)
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.SHSJ, 'YYYYMMDD'),
                    b.SQSJ
            ) a
    ) b
WHERE rn <= 10;
-- 采集时间符合玿 -- BGRQ CJSJ
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30361' GZBH,
    --'TB_GENETIC_REPORT' BM,
    --'CJSJ' ZDM,
    CWZ,
    COALESCE((CWL), 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.CJSJ CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE NOT(b.CJSJ IS NOT NULL)
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.SHSJ, 'YYYYMMDD'),
                    b.CJSJ
            ) a
    ) b
WHERE rn <= 10;
-- 检测时间符合率  -- BGRQ JCSJ
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30362' GZBH,
    --'TB_GENETIC_REPORT' BM,
    --'JCSJ' ZDM,
    CWZ,
    COALESCE((CWL), 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.JCSJ CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE NOT(b.JCSJ IS NOT NULL)
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.SHSJ, 'YYYYMMDD'),
                    b.JCSJ
            ) a
    ) b
WHERE rn <= 10;
-- 基因标本代码符合玿 -- BGRQ JYBBDM
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30363' GZBH,
    --'TB_GENETIC_REPORT' BM,
    --'JYBBDM' ZDM,
    CWZ,
    COALESCE((CWL), 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM(
                SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.JYBBDM CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE (
                        b.JYBBDM NOT IN (
                            '1',
                            '2',
                            '3',
                            '4',
                            '9999'
                        )
                    )
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.SHSJ, 'YYYYMMDD'),
                    b.JYBBDM
            ) a
    ) b
WHERE rn <= 10;
-- 检测项目代码符合率  -- BGRQ JCXMDM
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30364' GZBH,
    --'TB_GENETIC_REPORT' BM,
    --'JCXMDM' ZDM,
    CWZ,
    COALESCE((CWL), 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.JCXMDM CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE (
                        b.JCXMDM NOT IN (
                            '1',
                            '2',
                            '3',
                            '4',
                            '5',
                            '6',
                            '7',
                            '8',
                            '9999'
                        )
                    )
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.SHSJ, 'YYYYMMDD'),
                    b.JCXMDM
            ) a
    ) b
WHERE rn <= 10;
-- 是否存在基因变异点符合率  -- BGRQ SFBY
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM JGDM,
    '30366' GZBH,
    --'TB_GENETIC_REPORT' BM,
    --'SFBY' ZDM,
    CWZ,
    COALESCE((CWL), 0) CWL,
    'v1.2' bbh,
    CURRENT_TIMESTAMP
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(b.SHSJ, 'YYYYMMDD') AS YWRQ,
                    d.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    b.SFBY CWZ
                FROM DWD.TB_GENETIC_REPORT b
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON d ON b.yljgyqdm = d.yljgyqdm
                WHERE (
                        b.SFBY NOT IN ('1', '2')
                    )
                    AND b.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(b.SHSJ, 'YYYYMMDD') < '${ETIME}'
                GROUP BY d.FJ_YLJGYQDM,
                    TO_CHAR(b.SHSJ, 'YYYYMMDD'),
                    b.SFBY
            ) a
    ) b
WHERE rn <= 10;
--30405-30420
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30405 卡号符合玿
-- YZXDSJ KH
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM AS JGDM,
    '30405' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    KH CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        CASE
                            WHEN TRIM(t1.KLX) = '0'
                            AND t1.KH ~ '^[0-9A-Z]{9}$' THEN 1
                            WHEN TRIM(t1.KLX) = '1'
                            AND t1.KH ~ '^[0-9]{10,10}$' THEN 1
                            WHEN TRIM(t1.KLX) = '2'
                            AND t1.KH ~ '^[0-9]{15,15}$' THEN 1
                            WHEN TRIM(t1.KLX) = '3'
                            AND t1.KH IS NOT NULL THEN 1
                            WHEN TRIM(t1.KLX) = '4'
                            AND t1.KH IS NOT NULL THEN 1
                            WHEN TRIM(t1.KLX) = '9' THEN 1
                            ELSE 0
                        END
                    ) = 0
                    AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    AND TRIM(t1.KLX) in ('0', '1', '2', '3', '4', '9')
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    KH
            ) a
    ) b
WHERE rn <= 10;
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30406 卡类型符合率
-- YZXDSJ KLX
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM AS JGDM,
    '30406' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    KLX CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL T1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        (
                            KLX NOT IN (
                                '0',
                                '1',
                                '2',
                                '3',
                                '4',
                                '9'
                            )
                        )
                        AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    )
                    AND t1.SHSJ >= TO_TIMESTAMP('${BTIME}', 'YYYYMMDD')
                    AND t1.SHSJ < TO_TIMESTAMP('${ETIME}', 'YYYYMMDD')
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    KLX
            ) a
    ) b
WHERE rn <= 10;
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30407 下达科室编码符合玿
-- YZXDSJ XDKSBM
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM AS JGDM,
    '30407' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') AS YWRQ,
                    t2.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) AS CWL,
                    t1.XDKSBM CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        t1.XDKSBM NOT IN (
                            SELECT DISTINCT YYKSDM
                            FROM DWD.TB_DIC_DEPARTMENT
                        )
                        AND t1.XDKSBM IS NOT NULL
                    )
                    AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= '${BTIME}'
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < '${ETIME}'
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    t1.XDKSBM
            ) a
    ) b
WHERE rn <= 10;
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30408 医嘱下达医护人员ID符合玿
-- YZXDSJ YZXDYHRYID
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM AS JGDM,
    '30408' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    t1.YZXDYHRYID CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        NOT EXISTS (
                            SELECT 1
                            FROM DWD.TB_DIC_PRACTITIONER X
                                left join prefix_dq.T_DIC_YLJGDM_COMPARISON C on X.yljgyqdm = C.yljgyqdm
                            WHERE t1.YZXDYHRYID = X.YHRYID
                                and X.XGBZ = '1'
                                AND t2.FJ_YLJGYQDM = C.FJ_YLJGYQDM
                        )
                        OR (t1.YZXDYHRYID IS NULL)
                    )
                    AND t1.XGBZ = '1' --AND ( ERR_FLAG != '1' OR ERR_FLAG IS NULL )
                    AND t1.SHSJ < '${ETIME}'
                    AND t1.SHSJ >= '${BTIME}' --AND t1.bbh = 'v1.2'
                GROUP BY t2.FJ_YLJGYQDM,
                    t1.SHSJ,
                    t1.YZXDYHRYID
            ) a
    ) b
WHERE rn <= 10;
-- 30409 医嘱下达时间符合玿
-- YZXDSJ YZXDSJ
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM AS JGDM,
    '30409' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    YZXDSJ CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        (YZXDSJ IS NULL)
                        AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    )
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= TO_TIMESTAMP('${BTIME}', 'YYYYMMDD')
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < TO_TIMESTAMP('${ETIME}', 'YYYYMMDD')
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    YZXDSJ
            ) a
    ) b
WHERE rn <= 10;
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30410 执行科室编码符合玿
-- YZXDSJ ZXKSBM
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM AS JGDM,
    '30410' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') AS YWRQ,
                    t2.FJ_YLJGYQDM AS JGDM,
                    COALESCE(COUNT(1), 0) AS CWL,
                    ZXKSBM CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                    LEFT JOIN (
                        SELECT DISTINCT YYKSDM
                        FROM DWD.TB_DIC_DEPARTMENT
                    ) A ON t1.ZXKSBM = A.YYKSDM
                WHERE ZXKSBM IS NULL
                    AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= TO_TIMESTAMP('${BTIME}', 'YYYYMMDD')
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < TO_TIMESTAMP('${ETIME}', 'YYYYMMDD')
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    ZXKSBM
            ) a
    ) b
WHERE rn <= 10;
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30411 医嘱执行医护人员ID符合玿
-- YZXDSJ YZZXYHRYID
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ YWRQ,
    JGDM AS JGDM,
    '30411' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    YZZXYHRYID CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                    LEFT JOIN (
                        SELECT DISTINCT YHRYID
                        FROM DWD.TB_DIC_PRACTITIONER
                    ) A ON t1.YZZXYHRYID = A.YHRYID
                WHERE (
                        NOT EXISTS (
                            SELECT 1
                            FROM DWD.TB_DIC_PRACTITIONER X
                                left join prefix_dq.T_DIC_YLJGDM_COMPARISON C on X.yljgyqdm = C.yljgyqdm
                            WHERE t1.YZXDYHRYID = X.YHRYID
                                and X.XGBZ = '1'
                                AND t2.FJ_YLJGYQDM = C.FJ_YLJGYQDM
                        )
                        OR (t1.YZXDYHRYID IS NULL)
                    )
                    AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= TO_TIMESTAMP('${BTIME}', 'YYYYMMDD')
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < TO_TIMESTAMP('${ETIME}', 'YYYYMMDD')
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    YZZXYHRYID
            ) a
    ) b
WHERE rn <= 10;
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30412 医嘱执行时间符合玿
-- YZXDSJ YZZXSJ
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30412' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    t1.YZZXSJ CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        TO_CHAR(t1.YZZXSJ, 'YYYYMMDD') IS NULL
                        AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    )
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= TO_TIMESTAMP('${BTIME}', 'YYYYMMDD')
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < TO_TIMESTAMP('${ETIME}', 'YYYYMMDD')
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    t1.YZZXSJ
            ) a
    ) b
WHERE rn <= 10;
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30416 医嘱项目类型符合玿
-- YZXDSJ YZLX
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30416' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    YZLX CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        (
                            YZLX NOT IN (
                                '01',
                                '02',
                                '03',
                                '04',
                                '05',
                                '06',
                                '07',
                                '08',
                                '99'
                            )
                        )
                        AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    )
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= TO_TIMESTAMP('${BTIME}', 'YYYYMMDD')
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < TO_TIMESTAMP('${ETIME}', 'YYYYMMDD')
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    YZLX
            ) a
    ) b
WHERE rn <= 10;
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30417 用药途径代码符合玿
-- YZXDSJ YF
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30417' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    YF CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        NOT(
                            (
                                YZLX = '01'
                                AND (
                                    YF IN (
                                        '1',
                                        '2',
                                        '3',
                                        '4',
                                        '401',
                                        '402',
                                        '403',
                                        '404',
                                        '405',
                                        '5',
                                        '6',
                                        '601',
                                        '602',
                                        '603',
                                        '604',
                                        '605',
                                        '606',
                                        '607',
                                        '608',
                                        '609',
                                        '610',
                                        '611',
                                        '612',
                                        '699',
                                        '7',
                                        '9'
                                    )
                                )
                            )
                            OR (
                                YZLX <> '01'
                                AND YF = '-'
                            )
                        )
                        AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    )
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= TO_TIMESTAMP('${BTIME}', 'YYYYMMDD')
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < TO_TIMESTAMP('${ETIME}', 'YYYYMMDD')
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    YF
            ) a
    ) b
WHERE rn <= 10;
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30419 用药频次代码符合玿
-- YZXDSJ YYPCDM
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30419' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    YYPCDM CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        NOT(
                            (
                                YZLX = '01'
                                AND (
                                    YYPCDM IN (
                                        'QD',
                                        'BID',
                                        'TID',
                                        'QID',
                                        'Q30D',
                                        'QW',
                                        'Q2W',
                                        'BIW',
                                        'TIW',
                                        'Q30M',
                                        'Q1H',
                                        'Q2H',
                                        'Q3H',
                                        'Q4H',
                                        'Q5H',
                                        'Q6H',
                                        'Q8H',
                                        'Q12H',
                                        'Q72H',
                                        'QM',
                                        'QN',
                                        'QON',
                                        'ST',
                                        'QOD',
                                        'Q5D',
                                        'Q10D',
                                        'C12H',
                                        'C24H',
                                        'PRN',
                                        'AC',
                                        'AM'
                                    )
                                )
                            )
                            OR (
                                YZLX <> '01'
                                AND YYPCDM = '-'
                            )
                        )
                        AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    )
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= TO_TIMESTAMP('${BTIME}', 'YYYYMMDD')
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < TO_TIMESTAMP('${ETIME}', 'YYYYMMDD')
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    YYPCDM
            ) a
    ) b
WHERE rn <= 10;
-- 住院医嘱明细衿(TB_CIS_DRADVICE_DETAIL)
-- 30420 皮试判别符合玿
-- YZXDSJ SFPS
insert into prefix_dq.TB_DQ_ERROR_YWSJ_DETAIL (
        ywrq,
        jgdm,
        gzbh,
        CWZ,
        CWL,
        BBH,
        xgshj
    )
SELECT YWRQ AS YWRQ,
    JGDM AS JGDM,
    '30420' AS GZBH,
    CWZ AS CWZ,
    COALESCE(CWL, 0) AS CWL,
    'v1.2' AS bbh,
    CURRENT_TIMESTAMP AS xgshj
FROM (
        SELECT YWRQ,
            JGDM,
            CWZ,
            CWL,
            row_number() OVER (
                partition by ywrq,
                jgdm
                order by CWL desc
            ) rn
        FROM (
                SELECT TO_CHAR(t1.SHSJ, 'YYYYMMDD') YWRQ,
                    t2.FJ_YLJGYQDM JGDM,
                    COALESCE(COUNT(1), 0) CWL,
                    SFPS CWZ
                FROM DWD.TB_CIS_DRADVICE_DETAIL t1
                    LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
                WHERE (
                        NOT(
                            SFPS IN ('0', '1')
                        )
                        AND XGBZ = '1' --AND t1.bbh = 'v1.2'
                    )
                    AND SFPS IS NOT NULL
                    AND SFPS != ''
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') >= TO_TIMESTAMP('${BTIME}', 'YYYYMMDD')
                    AND TO_CHAR(t1.SHSJ, 'YYYYMMDD') < TO_TIMESTAMP('${ETIME}', 'YYYYMMDD')
                GROUP BY t2.FJ_YLJGYQDM,
                    TO_CHAR(t1.SHSJ, 'YYYYMMDD'),
                    SFPS
            ) a
    ) b
WHERE rn <= 10;