#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试 pymssql 导入
"""

def test_pymssql_import():
    """测试 pymssql 是否可以正常导入"""
    try:
        import pymssql
        print("✅ pymssql 导入成功!")
        print(f"版本: {pymssql.__version__}")
        return True
    except ImportError as e:
        print(f"❌ pymssql 导入失败: {e}")
        return False

def test_pandas_import():
    """测试 pandas 是否可以正常导入"""
    try:
        import pandas as pd
        print("✅ pandas 导入成功!")
        print(f"版本: {pd.__version__}")
        return True
    except ImportError as e:
        print(f"❌ pandas 导入失败: {e}")
        return False

def test_all_imports():
    """测试所有必要的包"""
    print("🔍 测试包导入...")
    print("=" * 40)
    
    success = True
    
    # 测试 pymssql
    if not test_pymssql_import():
        success = False
    
    # 测试 pandas
    if not test_pandas_import():
        success = False
    
    # 测试其他包
    packages = ['numpy', 'openpyxl', 'xlsxwriter']
    for package in packages:
        try:
            module = __import__(package)
            print(f"✅ {package} 导入成功!")
        except ImportError as e:
            print(f"❌ {package} 导入失败: {e}")
            success = False
    
    print("=" * 40)
    if success:
        print("🎉 所有包导入成功!")
    else:
        print("⚠️  部分包导入失败，请检查安装")
    
    return success

if __name__ == "__main__":
    test_all_imports() 