INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40001' AS GZBH
 ,JYJLSH JYJLSH
 ,case when FHGZSH>=0 then FHGZSH else '0.00' end FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT tt.YWRQ AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM TO_TIMESTAMP(tt.ywrq || '235959', 'YYYYMMDDHH24MISS'))::BIGINT ) / 86400.0, 2)) AS FHGZSH
 FROM DWD.TB_STAT_YWL_REPORT tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND tt.bbh = 'v1.2'
  AND YWRQ >= '${BTIME}'
  AND YWRQ < '${ETIME}'
 GROUP BY YWRQ
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40002' GZBH
 ,JYJLSH JYJLSH
 ,case when FHGZSH>=0 then FHGZSH else '0.00' end FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT tt.YWRQ AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM TO_TIMESTAMP(tt.ywrq || '235959', 'YYYYMMDDHH24MISS'))::BIGINT ) / 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_stat_ywsr_report tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND YWRQ >= '${BTIME}'
  AND YWRQ < '${ETIME}'
 GROUP BY YWRQ
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40003' GZBH
 ,JYJLSH JYJLSH
 ,case when FHGZSH>=0 then FHGZSH else '0.00' end FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT tt.YWRQ AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM TO_TIMESTAMP(tt.ywrq || '235959', 'YYYYMMDDHH24MISS'))::BIGINT ) / 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_stat_lis_report tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND YWRQ >= '${BTIME}'
  AND YWRQ < '${ETIME}'
 GROUP BY YWRQ
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40004' GZBH
 ,JYJLSH JYJLSH
 ,case when FHGZSH>=0 then FHGZSH else '0.00' end FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT tt.YWRQ AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM TO_TIMESTAMP(tt.ywrq || '235959', 'YYYYMMDDHH24MISS'))::BIGINT ) / 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_stat_ris_report tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND YWRQ >= '${BTIME}'
  AND YWRQ < '${ETIME}'
 GROUP BY YWRQ
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40005' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.GTHSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.GTHSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_his_mz_reg tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(GTHSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(GTHSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.GTHSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40006' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.STFSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.STFSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_his_mz_charge tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(STFSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(STFSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.STFSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40007' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.RYSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.RYSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_his_zy_adm_reg tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(RYSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(RYSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.RYSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40008' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.CYSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.CYSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_his_zy_dis_reg tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(CYSJ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(CYSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.CYSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40009' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.STFSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.STFSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_his_jz_charge tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(STFSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(STFSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.STFSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40010' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.JZKSRQ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.JZKSRQ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_yl_mz_medical_record tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(JZKSRQ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(JZKSRQ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.JZKSRQ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40011' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.KFRQ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.KFRQ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_cis_prescription_detail tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(KFRQ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(KFRQ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.KFRQ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40012' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.STFSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.STFSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_his_mz_fee_detail tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(STFSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(STFSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.STFSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40013' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.CYSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.CYSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_yl_zy_medical_record tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(CYSJ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(CYSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.CYSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40014' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SHSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SHSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_cis_dradvice_detail tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SHSJ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SHSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40015' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.STFSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.STFSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_his_zy_fee_detail tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(STFSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(STFSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.STFSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40016' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SHSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SHSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_lis_report tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SHSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40017' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SHSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SHSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_lis_indicators tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SHSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40018' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SHSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SHSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_lis_bacteria_result tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SHSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40019' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SHSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SHSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_lis_allergy_result tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SHSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40020' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SHSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SHSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_ris_report tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SHSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40021' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SHSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SHSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_ris_report2 tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SHSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40022' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SSJSSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SSJSSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_opration_detail tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SSJSSJ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(SSJSSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SSJSSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40023' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.ZDSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.ZDSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_ih_diagnosis_detail tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(ZDSJ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(ZDSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.ZDSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40024' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.CYSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.CYSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_cis_leavehospital_summary tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(CYSJ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(CYSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.CYSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40025' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.FYFSSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.FYFSSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_his_zy_fee_detail_fs tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(FYFSSJ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(FYFSSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.FYFSSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40026' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SSJSSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SSJSSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_his_opr_rec tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SSJSSJ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(SSJSSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SSJSSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40027' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SJJZSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SJJZSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_his_appointment tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SJJZSJ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(SJJZSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SJJZSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40028' GZBH
 ,JYJLSH JYJLSH
 ,case when FHGZSH>=0 then FHGZSH else '0.00' end FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT tt.YWRQ AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM TO_TIMESTAMP(tt.ywrq || '235959', 'YYYYMMDDHH24MISS'))::BIGINT ) / 86400.0, 2)) AS FHGZSH
 FROM DWD.TB_STAT_APPOINTMENT_REPORT tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND YWRQ >= '${BTIME}'
  AND YWRQ < '${ETIME}'
 GROUP BY YWRQ
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40029' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SHSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SHSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.TB_RIS_REPORT_DETAIL tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SHSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40030' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.GDRQ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.GDRQ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_ba_syjbk tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE --tt.bbh = 'v1.2'
  TO_CHAR(GDRQ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(GDRQ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.GDRQ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40031' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.GDRQ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.GDRQ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_ba_syzdk tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE --tt.bbh = 'v1.2'
  TO_CHAR(tt.GDRQ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(tt.GDRQ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.GDRQ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;


INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40032' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.GDRQ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.GDRQ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_ba_syssk tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE --tt.bbh = 'v1.2'
  TO_CHAR(tt.GDRQ, 'YYYYMMDD')  >= '${BTIME}'
  AND TO_CHAR(tt.GDRQ, 'YYYYMMDD')  < '${ETIME}'
 GROUP BY TO_CHAR(tt.GDRQ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;



INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40033' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.GDRQ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.GDRQ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_ba_syyek tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE --tt.bbh = 'v1.2'
  TO_CHAR(tt.GDRQ, 'YYYYMMDD')>= '${BTIME}'
  AND TO_CHAR(tt.GDRQ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.GDRQ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;

INSERT INTO  prefix_dq.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
 ,JGDM
 ,'40034' GZBH
 ,JYJLSH JYJLSH
 ,FHGZSH FHGZSH
 ,'v1.2' bbh
 ,current_timestamp XGSHJ
FROM (
 SELECT TO_CHAR(tt.SHSJ, 'YYYYMMDD') AS YWRQ
  ,t2.FJ_YLJGYQDM AS JGDM
  ,COUNT(1) AS JYJLSH
  ,sum(round((EXTRACT(EPOCH FROM tt.jlgxsj)::BIGINT - EXTRACT(EPOCH FROM tt.SHSJ)::BIGINT )/ 86400.0, 2)) AS FHGZSH
 FROM DWD.tb_genetic_report tt
 LEFT JOIN prefix_dq.T_DIC_YLJGDM_COMPARISON t2 ON tt.yljgyqdm = t2.yljgyqdm
 WHERE XGBZ = '1'
 --AND --tt.bbh = 'v1.2'
  AND TO_CHAR(tt.SHSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(tt.SHSJ, 'YYYYMMDD') < '${ETIME}'
 GROUP BY TO_CHAR(tt.SHSJ, 'YYYYMMDD')
  ,t2.FJ_YLJGYQDM
 ) t;





