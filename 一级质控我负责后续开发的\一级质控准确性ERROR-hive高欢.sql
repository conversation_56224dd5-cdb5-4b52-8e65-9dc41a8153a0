--30008 特殊科室标志符合率
-- 挂号表 TB_HIS_MZ_REG
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq,jgdm,gzbh,cwz,cwl,bbh,xgshj)
SELECT
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30008' AS GZBH,
    A.CWZ AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
        SELECT
            YWRQ,
            JGDM,
            CWZ,
            CWL,
            ROW_NUMBER() OVER (partition by YWRQ,JGDM order by CWL desc) AS rn
        FROM(
            SELECT
                date_format(t1.GTHSJ,'yyyyMMdd') as YWRQ,
                t2.FJ_YLJGYQDM as JGDM,
                t1.TSKSBZ as CWZ,
                NVL(COUNT(1), 0) as CWL
            FROM hive_wsjk_v12_prod_source_dwd.TB_HIS_MZ_REG t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
            WHERE (t1.TSKSBZ is null or t1.TSKSBZ NOT IN ('00001','00002','-'))   --特殊科室标志 ： 00001 ： 发热门诊 ； 00002 ： 国际诊疗 。 若未采集到或确实无相关业务信息可填写 “ - ”
                AND t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.GTHSJ,'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.GTHSJ,'yyyyMMdd') < '${ETIME}'
            GROUP BY date_format(t1.GTHSJ,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.TSKSBZ
            ) B
) A
where A.rn <= 10;


--30023 实际挂号时间符合率
--预约就诊记录表 TB_HIS_APPOINTMENT
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq,jgdm,gzbh,cwz,cwl,bbh,xgshj)
SELECT
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30023' AS GZBH,
    A.CWZ AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
        SELECT
            YWRQ,
            JGDM,
            CWZ,
            CWL,
            ROW_NUMBER() OVER (partition by YWRQ,JGDM order by CWL desc) AS rn
        FROM(
            SELECT
                date_format(t1.SJJZSJ,'yyyyMMdd') as YWRQ,
                t2.FJ_YLJGYQDM as JGDM,
                t1.SJGHSJ as CWZ,
                NVL(COUNT(1), 0) as CWL
            FROM hive_wsjk_v12_prod_source_dwd.TB_HIS_APPOINTMENT t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
            WHERE t1.SJGHSJ NOT rlike '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$'   --不符合格式的记录
                AND t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.SJJZSJ,'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.SJJZSJ,'yyyyMMdd') < '${ETIME}'
            GROUP BY date_format(t1.SJJZSJ,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.SJGHSJ
            ) B
) A
where A.rn <= 10;

--30025 预约患者证件类型符合率
--预约就诊记录表 TB_HIS_APPOINTMENT
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq,jgdm,gzbh,cwz,cwl,bbh,xgshj)
SELECT
    A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30025' AS GZBH,
    A.CWZ AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER (PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT
            date_format(t1.SJJZSJ,'yyyyMMdd') as YWRQ,
            t2.FJ_YLJGYQDM  as JGDM,
            t1.ZJLX as CWZ,
            NVL(COUNT(1),0) AS CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_HIS_APPOINTMENT t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE t1.ZJLX NOT IN ('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '51','52','99')
            AND t1.XGBZ = '1'
            AND t1.bbh = 'v1.2'
            AND date_format(t1.SJJZSJ, 'yyyyMMdd') >= '${BTIME}'
            AND date_format(t1.SJJZSJ, 'yyyyMMdd') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,date_format(t1.SJJZSJ,'yyyyMMdd'),t1.ZJLX
    ) B
) A
WHERE A.rn <= 10;

--30039 特殊科室标志符合率
--出院登记表 TB_HIS_ZY_DIS_REG
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq,jgdm,gzbh,cwz,cwl,bbh,xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM               AS JGDM,
    '30039'              AS GZBH,
    A.CWZ                AS CWZ,
    A.CWL                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
        SELECT
            YWRQ,
            JGDM,
            CWZ,
            CWL,
            ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
        FROM(
            SELECT
                date_format(t1.CYSJ, 'yyyyMMdd') as YWRQ,
                t2.FJ_YLJGYQDM as JGDM,
                t1.TSKSBZ as CWZ,
                NVL(COUNT(1), 0) as CWL
            FROM hive_wsjk_v12_prod_source_dwd.TB_HIS_ZY_DIS_REG t1
                LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
            WHERE (t1.TSKSBZ is null OR t1.TSKSBZ NOT IN ('00001', '00002','-'))
                AND t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.CYSJ, 'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.CYSJ, 'yyyyMMdd') < '${ETIME}'
                GROUP BY t2.FJ_YLJGYQDM,date_format(t1.CYSJ, 'yyyyMMdd'),t1.TSKSBZ
    ) B
) A
WHERE A.rn <= 10;



--30059 检查类型符合率
--医学影像检查报告数量日汇总  TB_STAT_RIS_REPORT
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM               AS JGDM,
    '30059'              AS GZBH,
    A.CWZ                AS CWZ,
    A.CWL                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
    select
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT
            date_format(t1.YWRQ, 'yyyyMMdd') as YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.EXAMTYPE as CWZ,
            NVL(COUNT(1), 0) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_STAT_RIS_REPORT t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (t1.EXAMTYPE is null or t1.EXAMTYPE NOT IN ('01','02','03','04','05','06','07','08','09','10','11','12'))
                AND t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.YWRQ, 'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.YWRQ, 'yyyyMMdd') < '${ETIME}'
        GROUP BY t2.FJ_YLJGYQDM,date_format(t1.YWRQ, 'yyyyMMdd'),t1.EXAMTYPE
    ) B
) A
WHERE A.rn <= 10;


--30062 证件号码符合率
--患者信息表 TB_YL_PATIENT_INFORMATION
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30062' AS GZBH,
    A.CWZ AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
        SELECT
            YWRQ,
            JGDM,
            CWZ,
            CWL,
            ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
        FROM(
            SELECT
                date_format(CURRENT_TIMESTAMP,'yyyyMMdd') AS YWRQ,
                t2.FJ_YLJGYQDM as JGDM,
                t1.ZJLX as CWZ,
                SUM(
                    CASE
                        WHEN trim(t1.ZJLX) = '01' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[1-9][0-9]{5}(19|20)?[0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{3}[0-9Xx]$') THEN 1
                        WHEN trim(t1.ZJLX) = '02' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '.+' )THEN 1  --至少有一个字符（任意内容）就算合格
                        WHEN trim(t1.ZJLX) = '03' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[a-zA-Z0-9]{5,17}$') THEN 1   --5到17位的字母或数字
                        WHEN trim(t1.ZJLX) = '04' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[a-zA-Z0-9]{7,21}$') THEN 1 --7到21位的字母或数字
                        WHEN trim(t1.ZJLX) = '06' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[HMhm][0-9]{8,10}$') THEN 1 --H或M开头，8到10位的数字
                        WHEN trim(t1.ZJLX) = '07' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[0-9]{8}$|^[a-zA-Z0-9]{10}$') THEN 1 --8位数字或10位字母或数字
                        WHEN trim(t1.ZJLX) = '08' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[1-9][0-9]{5}(19|20)?[0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{3}[0-9Xx]$') THEN 1 --18位数字或18位数字+X或x
                        WHEN trim(t1.ZJLX) = '09' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[a-zA-Z0-9]{7,21}$' ) THEN 1
                        WHEN trim(t1.ZJLX) = '10' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[a-zA-Z0-9]{10,18}$') THEN 1
                        WHEN trim(t1.ZJLX) = '11' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[a-zA-Z0-9]{10,18}$') THEN 1
                        WHEN trim(t1.ZJLX) = '51' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[A-Z0-9]{15,18}$') THEN 1
                        WHEN trim(t1.ZJLX) = '52' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '^[a-zA-Z0-9]{7,18}$') THEN 1
                        WHEN trim(t1.ZJLX) = '99' AND (t1.ZJHM is null or t1.ZJHM NOT RLIKE '.+' ) THEN 1
                    ELSE 0
                END
                ) AS CWL
            FROM hive_wsjk_v12_prod_source_dwd.TB_YL_PATIENT_INFORMATION t1
                LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
            WHERE t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
            GROUP BY date_format(CURRENT_TIMESTAMP,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.ZJLX
        ) B
) A
WHERE A.rn <= 10;



--30063 证件类型符合率
--患者信息表 TB_YL_PATIENT_INFORMATION
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT 
    A.YWRQ               AS YWRQ,
    A.JGDM               AS JGDM,
    '30063'              AS GZBH,
    A.CWZ                AS CWZ,
    A.CWL                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            date_format(CURRENT_TIMESTAMP,'yyyyMMdd') AS YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.ZJLX as CWZ,
            SUM(
                case
                    when t1.ZJLX  NOT IN ('01','02','03','04','06','07','08','09','10','11','51','52','99') then 1
                    else 0
                end
            ) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_YL_PATIENT_INFORMATION t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE t1.XGBZ = '1'
            AND t1.bbh = 'v1.2'
        GROUP BY date_format(CURRENT_TIMESTAMP,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.ZJLX
    ) B
) A
WHERE A.rn <= 10;



--30064 性别符合率
--患者信息表 TB_YL_PATIENT_INFORMATION
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT 
    A.YWRQ               AS YWRQ,
    A.JGDM               AS JGDM,
    '30064'              AS GZBH,
    A.CWZ                AS CWZ,
    A.CWL                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            date_format(CURRENT_TIMESTAMP,'yyyyMMdd') AS YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.XB as CWZ,
            SUM(
                case
                    when (t1.XB is null or t1.XB NOT IN ('1','2','3')) then 1
                    else 0
                end
            ) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_YL_PATIENT_INFORMATION t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE t1.XGBZ = '1'
            AND t1.bbh = 'v1.2'
        GROUP BY date_format(CURRENT_TIMESTAMP,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.XB
    ) B
) A
WHERE A.rn <= 10;



--30066 婚姻状况符合率
--患者信息表 TB_YL_PATIENT_INFORMATION
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT 
    A.YWRQ               AS YWRQ,
    A.JGDM               AS JGDM,
    '30066'              AS GZBH,
    A.CWZ                AS CWZ,
    A.CWL                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            date_format(CURRENT_TIMESTAMP,'yyyyMMdd') AS YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.HYZK as CWZ,
            SUM(
                case
                    when (t1.HYZK is null or t1.HYZK NOT IN ('10','20','30','40','90','-') ) then 1
                    --10未婚、20已婚、30丧偶、40离婚、90未说明的婚姻状况
                    else 0
                end
            ) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_YL_PATIENT_INFORMATION t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE t1.XGBZ = '1'
            AND t1.bbh = 'v1.2'
        GROUP BY date_format(CURRENT_TIMESTAMP,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.HYZK
    ) B
) A
WHERE A.rn <= 10;


--30068 出生地符合率
--患者信息表 TB_YL_PATIENT_INFORMATION
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT 
    A.YWRQ               AS YWRQ,
    A.JGDM               AS JGDM,
    '30068'              AS GZBH,
    A.CWZ                AS CWZ,
    A.CWL                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            date_format(CURRENT_TIMESTAMP,'yyyyMMdd') AS YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.CSD as CWZ,
            SUM(
                case
                    when t1.CSD is null then 1
                    when t1.CSD != '-' and t3.XZQGHDM is null  then 1
                    when (t1.HZLY != '4' or t1.ZJLX NOT IN ('03','06','07')) and t1.CSD = '-' then 1  --03护照、06、07通行证
                    else 0
                end
            ) as CWL  --出生地必须符合国标GB/T2260-2007执行。填写“-”，仅限于外国友人持护照/通行证类证件就医这种情况
        FROM hive_wsjk_v12_prod_source_dwd.TB_YL_PATIENT_INFORMATION t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
            LEFT JOIN cen_dic.tb_dic_xzhqh t3 ON t1.CSD = t3.XZQGHDM
        WHERE t1.XGBZ = '1'
            AND t1.bbh = 'v1.2'
        GROUP BY date_format(CURRENT_TIMESTAMP,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.CSD
    ) B
) A
WHERE A.rn <= 10;


--30069 民族符合率
--患者信息表 TB_YL_PATIENT_INFORMATION
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT 
    A.YWRQ               AS YWRQ,
    A.JGDM               AS JGDM,
    '30069'              AS GZBH,
    A.CWZ                AS CWZ,
    A.CWL                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            date_format(CURRENT_TIMESTAMP,'yyyyMMdd') AS YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.MZ as CWZ,
            SUM(
                case
                    when (t1.MZ is  null or t1.MZ NOT IN ('01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','21'
                    ,'22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37','38','39','40','41','42','43','44','45','46'
                    ,'47','48','49','50','51','52','53','54','55','56','-') )then 1
                    --国家标准GB/T 3304-199   56个民族 数字代码
                    else 0
                end
            ) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_YL_PATIENT_INFORMATION t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE t1.XGBZ = '1'
            AND t1.bbh = 'v1.2'
        GROUP BY date_format(CURRENT_TIMESTAMP,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.MZ
    ) B
) A
WHERE A.rn <= 10;  


--30070 国籍符合率
--患者信息表 TB_YL_PATIENT_INFORMATION
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT 
    A.YWRQ               AS YWRQ,
    A.JGDM               AS JGDM,
    '30070'              AS GZBH,
    A.CWZ                AS CWZ,
    A.CWL                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            date_format(CURRENT_TIMESTAMP,'yyyyMMdd') AS YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.GJ as CWZ,
            SUM(
                case
                    when t1.GJ is null then 1
                    when t1.GJ != '-' and t3.code is null then 1
                                        --按国家标准DIC_CBT2659_1_2022执行，采用三位英文代码。例如：国籍：中国，码值：CHN，则填CHN。若无，则填“-”
                    else 0
                end
            ) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_YL_PATIENT_INFORMATION t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
            LEFT JOIN cen_dic.tb_dic_country t3 on t1.GJ = t3.code
        WHERE t1.XGBZ = '1'
            AND t1.bbh = 'v1.2'
        GROUP BY date_format(CURRENT_TIMESTAMP,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.GJ
    ) B
) A
WHERE A.rn <= 10;


--30071 联系人关系符合率
--患者信息表 TB_YL_PATIENT_INFORMATION
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT 
    A.YWRQ               AS YWRQ,
    A.JGDM               AS JGDM,
    '30071'              AS GZBH,
    A.CWZ                AS CWZ,
    A.CWL                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            date_format(CURRENT_TIMESTAMP,'yyyyMMdd') AS YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.LXRGX as CWZ,
            SUM(
                case
                    WHEN (t1.LXRGX is null or t1.LXRGX NOT IN ('1','2','3','4','5','6','7','9','-')) then 1
                    --联系人关系必须符合国家标准GB/T 4761-2008。若未采集到或确实无相关业务信息可填写“-”
                    else 0
                end
            ) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_YL_PATIENT_INFORMATION t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE t1.XGBZ = '1'
            AND t1.bbh = 'v1.2'
        GROUP BY date_format(CURRENT_TIMESTAMP,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.LXRGX
    ) B
) A 
WHERE A.rn <= 10;


--30073 密级符合率
--患者信息表 TB_YL_PATIENT_INFORMATION
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT 
    A.YWRQ               AS YWRQ,
    A.JGDM               AS JGDM,
    '30073'              AS GZBH,
    A.CWZ                AS CWZ,
    A.cwl                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            DATE_FORMAT(CURRENT_TIMESTAMP,'yyyyMMdd') AS YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.MJ as CWZ,
            SUM(
                case
                    when (t1.MJ is null or substring(t1.MJ,1,1) NOT IN ('0','1')) then 1
                    --密级填报：首位数字仅限填写‘0’、‘1’
                    else 0
                end
            ) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_YL_PATIENT_INFORMATION t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE t1.XGBZ = '1'
            AND t1.bbh = 'v1.2'
        GROUP BY date_format(CURRENT_TIMESTAMP,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.MJ
    ) B
) A
WHERE A.rn <= 10;




--30077 密级符合率
--医学影像患者信息 TB_RIS_PATIENT_INFORMATION
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT 
    A.YWRQ               AS YWRQ,
    A.JGDM               AS JGDM,
    '30077'              AS GZBH,
    A.CWZ                AS CWZ,
    A.CWL                AS CWL,
    'v1.2'               AS BBH,
    CURRENT_TIMESTAMP    AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            DATE_FORMAT(CURRENT_TIMESTAMP,'yyyyMMdd') AS YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.MJ as CWZ,
            SUM(
                case
                    when (t1.MJ is null or substring(t1.MJ,1,1) NOT IN ('0','1')) then 1
                    --密级填报：首位数字仅限填写‘0’、‘1’
                    else 0
                end
            ) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_RIS_PATIENT_INFORMATION t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE t1.XGBZ = '1'
            AND t1.bbh = 'v1.2'
        GROUP BY date_format(CURRENT_TIMESTAMP,'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.MJ
    ) B
) A
WHERE A.rn <= 10;

--30084 门诊诊断编码(主要诊断)符合率
--门诊就诊记录表    
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30084' AS GZBH,
    A.CWZ  AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(   
        SELECT 
            date_format(t1.JZKSRQ,'yyyyMMdd') AS YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.JZZDBM as CWZ,
            sum(
                case 
                    when (t1.JZZDBM = t3.ZYZD and t1.JZZDBM = t4.DMXDMZ and t4.DMXDMZ is not null) then 0
                    when t1.JZZDBM = '-' then 0
                    else 1
                end
            ) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_YL_MZ_MEDICAL_RECORD t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
            LEFT JOIN hive_wsjk_v12_prod_source_dwd.TB_BA_SYJBK t3 ON t1.yljgyqdm = t3.yljgyqdm and t1.kh = t3.kh and t1.klx = t3.klx
            LEFT JOIN cen_dic.tmp_dic_disease_all t4 ON t1.JZZDBM = t4.DMXDMZ
            where    t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.JZKSRQ, 'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.JZKSRQ, 'yyyyMMdd') < '${ETIME}'
        GROUP BY date_format(t1.JZKSRQ, 'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.JZZDBM
    ) B 
) A
WHERE A.rn <= 10;


--30085 中医诊断编码符合率
--门诊就诊记录表 TB_YL_MZ_MEDICAL_RECORD
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30085' AS GZBH,
    A.CWZ AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT date_format(t1.JZKSRQ, 'yyyyMMdd') as YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.ZYZD as CWZ,
            NVL(COUNT(1),0) AS CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_YL_MZ_MEDICAL_RECORD t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE  (
            -- 格式不正确
            t1.ZYZD NOT REGEXP '^\\([AB]\\d{2}(\\.[0-9]{2})*\\.?(\\+[AB]\\d{2}(\\.[0-9]{2})*\\.?)*,C\\d{2}(\\.[0-9]{2})*\\.?\\)$'
            OR
            -- 或者编码不在字典表中
            EXISTS (
                SELECT 1 
                FROM (
                    SELECT TRIM(single_code) as single_code
                    FROM (
                        SELECT explode(split(regexp_replace(regexp_replace(t1.ZYZD, '[()]', ''), '[\\+,]', '|'), '\\|')) as single_code
                    ) tmp
                    WHERE single_code != '' AND single_code IS NOT NULL
                ) split_codes
                WHERE split_codes.single_code NOT IN (SELECT DMXDMZ FROM cen_dic.tmp_dic_disease_all)
            )
        )           --证型编码+……+证型编码+证型编码，治法编码
                AND t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.JZKSRQ, 'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.JZKSRQ, 'yyyyMMdd') < '${ETIME}'
        GROUP BY date_format(t1.JZKSRQ, 'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.ZYZD
    ) B 
) A
WHERE A.rn <= 10;

--30093 医护人员ID符合率
--门诊医嘱明细表 TB_CIS_PRESCRIPTION_DETAIL
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30093' AS GZBH,
    A.CWZ AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT date_format(t1.KFRQ, 'yyyyMMdd') as YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.YHRYID as CWZ,
            NVL(COUNT(1),0) AS CWL  
        FROM hive_wsjk_v12_prod_source_dwd.TB_CIS_PRESCRIPTION_DETAIL t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (t1.YHRYID NOT IN(select distinct YHRYID from hive_wsjk_v12_prod_source_ods.TB_DIC_PRACTITIONER) OR t1.YHRYID is  null )
                AND t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.KFRQ, 'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.KFRQ, 'yyyyMMdd') < '${ETIME}'
        GROUP BY date_format(t1.KFRQ, 'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.YHRYID
    ) B 
) A
WHERE A.rn <= 10;


--30095 项目类型符合率
--门诊医嘱明细表 TB_CIS_PRESCRIPTION_DETAIL
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30095' AS GZBH,
    A.CWZ AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT date_format(t1.KFRQ, 'yyyyMMdd') as YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.CFLX as CWZ,
            NVL(COUNT(1),0) AS CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_CIS_PRESCRIPTION_DETAIL t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (t1.CFLX is null or t1.CFLX NOT IN ('01','02','03','04','05','06','07','08','99'))  --项目类型必须符合CV06.00.229医嘱项目类型代码表
                AND t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.KFRQ, 'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.KFRQ, 'yyyyMMdd') < '${ETIME}'
        GROUP BY date_format(t1.KFRQ, 'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.CFLX
    ) B 
) A
WHERE A.rn <= 10;


--30096 项目明细编码(医保)符合率
--门诊医嘱明细表 TB_CIS_PRESCRIPTION_DETAIL
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30096' AS GZBH,
    A.CWZ AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            date_format(t1.KFRQ, 'yyyyMMdd') as YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.MXXMBMYB as CWZ,
            SUM(case
                when t1.MXXMBMYB is null then 1
                -- 药品类：国家医保代码
                when trim(t1.CFLX) = '01' and a.YPBM is null then 1  
                -- 耗材类：国家医保代码，无则用物价代码
                when trim(t1.CFLX) = '06' and b.LABLE = 'HC' and (b.YBBM is null and b.WJBM is null) then 1  
                -- 非药品非耗材：国家医保代码或上海医保代码，无则用物价代码
                when trim(t1.CFLX) in('02','03','04','05','07','08','99') and c.LABLE = 'ZLXM' 
                    and (c.GJYBBM_ZLXM is null and c.WJBM is  null) then 1  
                else 0
            end ) as CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_CIS_PRESCRIPTION_DETAIL t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
            LEFT JOIN cen_dic.tmp_dic_medicines a on t1.MXXMBMYB = a.YPBM 
            LEFT JOIN cen_dic.tmp_dic_materials b on t1.MXXMBMYB = b.YBBM 
            LEFT JOIN cen_dic.tmp_dic_materials c on t1.MXXMBMYB = c.GJYBBM_ZLXM 
        WHERE   t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.KFRQ, 'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.KFRQ, 'yyyyMMdd') < '${ETIME}'
        GROUP BY date_format(t1.KFRQ, 'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.MXXMBMYB
        ) B 
    ) A
WHERE A.rn <= 10;



--30097 剂型代码符合率
--门诊医嘱明细表 TB_CIS_PRESCRIPTION_DETAIL
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30097' AS GZBH,
    A.CWZ AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            date_format(t1.KFRQ, 'yyyyMMdd') as YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.JXDM as CWZ,
            NVL(COUNT(1),0) AS CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_CIS_PRESCRIPTION_DETAIL t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (t1.JXDM is null or t1.JXDM NOT IN (  '00','01','0101','0102','0103','0104','0105','0106','0107','0199',
                            '02','0201','0202','0203','0204','0299',
                            '03','0301','0302','0303','0304','0305','0306','0399',
                            '04','0401','0402','0403','0404','0405','0406','0499',
                            '05','0501','0502','0503','0504','0505','0506','0507','0508','0509','0510','0599',
                            '06','0601','0602','0699',
                            '07','0701','0702','0703','0704','0705','0706','0707','0708','0709','0710','0711','0799',
                            '08','0801','0802','0803','0804','0899',
                            '09','0901','0902','0903','0904','0999',
                            '10','1001','1002','1003','1004','1005','1099',
                            '11','1101','1102','1199',
                            '12','1201','1202','1203','1204','1299',
                            '13','1301','1302','1303','1399',
                            '14','1401','1402','1403','1499',
                            '15','1501','1502','1503','1504','1505','1506','1599',
                            '16','1601','1603','1604','1605','1699',
                            '17','1701','1702','1703','1704','1705','1706','1799',
                            '18','1801','1802','1803','1899',
                            '19','1901','1902','1903','1904','1905','1906','1907','1999',
                            '20','2001','2002','2099',
                            '21','2101','2102','2199',
                            '22','2201','2202','2203','2204','2205','2206','2207','2208','2209','2210','2211','2212','2213','2214','2215','2299',
                            '23','2301','2302','2303','2399',
                            '24','2401','2402','2403','2499',
                            '25','2501','2502','2503','2599',
                            '26','2601','2602','2603','2604','2605','2606','2699',
                            '27','2701','2702','2703','2799',
                            '90','9001','9002','9099',
                            '99','ZA' ))
                AND t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.KFRQ, 'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.KFRQ, 'yyyyMMdd') < '${ETIME}'
        GROUP BY date_format(t1.KFRQ, 'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.JXDM
    ) B 
) A
WHERE A.rn <= 10;


--30098 用药频次代码符合率
--门诊医嘱明细表 TB_CIS_PRESCRIPTION_DETAIL
INSERT INTO hive_wsjk_v12_prod_source_dwd.TB_DQ_ERROR_YWSJ_DETAIL(ywrq, jgdm, gzbh, cwz, cwl, bbh, xgshj)
SELECT A.YWRQ AS YWRQ,
    A.JGDM AS JGDM,
    '30098' AS GZBH,
    A.CWZ AS CWZ,
    A.CWL AS CWL,
    'v1.2' AS BBH,
    CURRENT_TIMESTAMP AS XGSHJ
FROM(
    SELECT
        YWRQ,
        JGDM,
        CWZ,
        CWL,
        ROW_NUMBER() OVER(PARTITION BY YWRQ,JGDM ORDER BY CWL DESC) AS rn
    FROM(
        SELECT 
            date_format(t1.KFRQ, 'yyyyMMdd') as YWRQ,
            t2.FJ_YLJGYQDM as JGDM,
            t1.YYPCSDM as CWZ,
            NVL(COUNT(1),0) AS CWL
        FROM hive_wsjk_v12_prod_source_dwd.TB_CIS_PRESCRIPTION_DETAIL t1
            LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
        WHERE (t1.YYPCSDM is null or t1.YYPCSDM NOT IN ('QD','BID','TID','QID','Q30D','QW','Q2W','BIW','TIW','Q30M',
                            'Q1H','Q2H','Q3H','Q4H','Q5H','Q6H','Q8H','Q12H','Q72H',
                            'QM','QN','QON','ST','QOD','Q5D','Q10D',
                            'C12H','C24H','PRN','AC','AM'))
                AND t1.XGBZ = '1'
                AND t1.bbh = 'v1.2'
                AND date_format(t1.KFRQ, 'yyyyMMdd') >= '${BTIME}'
                AND date_format(t1.KFRQ, 'yyyyMMdd') < '${ETIME}'
        GROUP BY date_format(t1.KFRQ, 'yyyyMMdd'),t2.FJ_YLJGYQDM,t1.YYPCSDM
    ) B 
) A
WHERE A.rn <= 10;
