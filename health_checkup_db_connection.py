import pymssql
import pandas as pd
from datetime import datetime
import os

class HealthCheckupDB:
    """
    健康体检数据库连接类
    """
    
    def __init__(self, server, database, username, password, port=1433):
        """
        初始化数据库连接参数
        """
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.port = port
        self.conn = None
    
    def connect(self):
        """
        连接数据库
        """
        try:
            self.conn = pymssql.connect(
                server=self.server,
                database=self.database,
                user=self.username,
                password=self.password,
                port=self.port,
                charset='utf8'
            )
            print(f"✅ 成功连接到数据库: {self.database}")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """
        断开数据库连接
        """
        if self.conn:
            self.conn.close()
            print("🔒 数据库连接已关闭")
    
    def execute_query(self, sql_query, return_dataframe=True):
        """
        执行查询语句
        """
        if not self.conn:
            print("❌ 请先连接数据库")
            return None
        
        try:
            cursor = self.conn.cursor()
            cursor.execute(sql_query)
            
            if return_dataframe:
                # 获取列名
                columns = [desc[0] for desc in cursor.description]
                # 获取数据
                rows = cursor.fetchall()
                # 转换为 DataFrame
                df = pd.DataFrame(rows, columns=columns)
                cursor.close()
                return df
            else:
                # 返回原始结果
                result = cursor.fetchall()
                cursor.close()
                return result
                
        except Exception as e:
            print(f"❌ 查询执行失败: {str(e)}")
            return None
    
    def execute_non_query(self, sql_query):
        """
        执行非查询语句（INSERT, UPDATE, DELETE）
        """
        if not self.conn:
            print("❌ 请先连接数据库")
            return False
        
        try:
            cursor = self.conn.cursor()
            cursor.execute(sql_query)
            self.conn.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            print(f"✅ 执行成功，影响行数: {affected_rows}")
            return True
        except Exception as e:
            print(f"❌ 执行失败: {str(e)}")
            self.conn.rollback()
            return False
    
    def get_table_info(self, table_name):
        """
        获取表结构信息
        """
        sql = f"""
        SELECT 
            COLUMN_NAME as 字段名,
            DATA_TYPE as 数据类型,
            IS_NULLABLE as 允许空值,
            COLUMN_DEFAULT as 默认值
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = '{table_name}'
        ORDER BY ORDINAL_POSITION
        """
        return self.execute_query(sql)
    
    def get_table_count(self, table_name):
        """
        获取表记录数
        """
        sql = f"SELECT COUNT(*) as 记录数 FROM {table_name}"
        result = self.execute_query(sql)
        if result is not None and not result.empty:
            return result.iloc[0]['记录数']
        return 0
    
    def export_to_excel(self, sql_query, filename, sheet_name='Sheet1'):
        """
        将查询结果导出到Excel
        """
        df = self.execute_query(sql_query)
        if df is not None:
            try:
                df.to_excel(filename, sheet_name=sheet_name, index=False)
                print(f"✅ 数据已导出到: {filename}")
                return True
            except Exception as e:
                print(f"❌ 导出失败: {str(e)}")
                return False
        return False

def example_usage():
    """
    使用示例
    """
    # 数据库连接配置
    config = {
        'server': 'your_server_name',  # 替换为您的服务器名称
        'database': 'your_database_name',  # 替换为您的数据库名称
        'username': 'your_username',  # 替换为您的用户名
        'password': 'your_password',  # 替换为您的密码
        'port': 1433
    }
    
    # 创建数据库连接对象
    db = HealthCheckupDB(**config)
    
    # 连接数据库
    if db.connect():
        try:
            # 示例1: 查询数据库基本信息
            print("\n📋 数据库基本信息:")
            info_sql = """
            SELECT 
                @@VERSION as 数据库版本,
                DB_NAME() as 当前数据库,
                GETDATE() as 当前时间
            """
            result = db.execute_query(info_sql)
            if result is not None:
                print(result)
            
            # 示例2: 查询所有表
            print("\n📋 数据库中的所有表:")
            tables_sql = """
            SELECT TABLE_NAME as 表名
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
            """
            result = db.execute_query(tables_sql)
            if result is not None:
                print(result)
            
            # 示例3: 查询特定表的结构（假设表名为 'health_checkup'）
            print("\n📋 表结构信息:")
            # table_info = db.get_table_info('health_checkup')
            # if table_info is not None:
            #     print(table_info)
            
            # 示例4: 查询特定表的记录数
            print("\n📋 表记录数:")
            # count = db.get_table_count('health_checkup')
            # print(f"health_checkup 表记录数: {count}")
            
            # 示例5: 导出数据到Excel
            print("\n📋 导出数据示例:")
            # export_sql = "SELECT TOP 100 * FROM health_checkup"
            # db.export_to_excel(export_sql, 'health_checkup_data.xlsx', '健康体检数据')
            
        finally:
            # 关闭连接
            db.disconnect()

def load_sql_file(file_path):
    """
    加载SQL文件内容
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except Exception as e:
        print(f"❌ 读取SQL文件失败: {str(e)}")
        return None

if __name__ == "__main__":
    print("🏥 健康体检数据库连接示例")
    print("=" * 60)
    
    # 运行示例
    example_usage()
    
    print("\n" + "=" * 60)
    print("📝 使用说明:")
    print("1. 修改 config 字典中的数据库连接参数")
    print("2. 使用 HealthCheckupDB 类进行数据库操作")
    print("3. 支持查询、导出Excel等功能")
    print("4. 可以加载项目中的SQL文件执行")
    print("5. 记得在操作完成后调用 disconnect() 关闭连接") 