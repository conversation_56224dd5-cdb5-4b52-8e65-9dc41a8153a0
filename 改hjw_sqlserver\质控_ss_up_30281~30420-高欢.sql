UPDATE DWD.dbo.TB_BA_SYJBK
SET war_flag = '1',
    war_note = ISNULL(war_note, '') + '&30287'
WHERE (
    CASE
        WHEN PATINDEX('%[0-9]', MZZD_ZY) > 0
        OR PATINDEX('%[0-9].[0-9]', MZZD_ZY) > 0
        OR PATINDEX('%[0-9].[0-9][0-9]', MZZD_ZY) > 0
        OR PATINDEX('%[0-9].[0-9][0-9][0-9]', MZZD_ZY) > 0 THEN 1
        ELSE 0
    END
) = 0
and convert(varchar(8),GDRQ,112) >= '${BTIME}'
and convert(varchar(8),GDRQ,112) < '${ETIME}'
AND (war_note not like '%30287%' or war_note is null) AND send_flag='0';

UPDATE DWD.dbo.TB_GENETIC_REPORT
SET war_flag = '1',
    war_note = ISNULL(war_note, '') + '&30352'
WHERE (
        CASE
                    -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                    WHEN trim(KLX) = '0'
                    AND PATINDEX('[^0-9A-Z]', KH) = 0 
                    AND datalength(KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                    WHEN trim(KLX) = '1'
                    AND PATINDEX('[^0-9]', KH) = 0
                    AND datalength(KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                    WHEN trim(KLX) = '2'
                    AND PATINDEX('[^0-9]', KH) = 0
                    AND datalength(KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                    WHEN trim(KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                    WHEN trim(KLX) = '9' THEN 1 -- 默认情况
                    ELSE 0
        END
    ) = 0
    and BGRQ >= '${BTIME}'
    and BGRQ < '${ETIME}'
    AND (
        war_note not like '%30352%'
        or war_note is null
    )
    AND send_flag = '0';



UPDATE DWD.dbo.TB_CIS_DRADVICE_DETAIL
SET war_flag = '1',
    war_note = ISNULL(war_note, '') + '&30405'
WHERE (
        CASE
                    -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                    WHEN trim(KLX) = '0'
                    AND PATINDEX('[^0-9A-Z]', KH) = 0 
                    AND datalength(KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                    WHEN trim(KLX) = '1'
                    AND PATINDEX('[^0-9]', KH) = 0
                    AND datalength(KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                    WHEN trim(KLX) = '2'
                    AND PATINDEX('[^0-9]', KH) = 0
                    AND datalength(KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                    WHEN trim(KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                    WHEN trim(KLX) = '9' THEN 1 -- 默认情况
                    ELSE 0
        END
    ) = 0
    and convert(varchar(8), SHSJ, 112) >= '${BTIME}'
    and convert(varchar(8), SHSJ, 112) < '${ETIME}'
    AND (
        war_note not like '%30405%'
        or war_note is null
    )
    AND send_flag = '0';