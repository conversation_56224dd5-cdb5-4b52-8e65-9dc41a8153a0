
TRUNCATE TABLE prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj;
--T_PERSONAL_RESERVATION 个人体检信息
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'T_PERSONAL_RESERVATION' tbname,
count(*) sjl,
CONVERT(VARCHAR(8), b.<PERSON>, 112) ywrq
from dwd_tj.dbo.T_PERSONAL_RESERVATION a
left join dwd_tj.dbo.T_AUDIT_FINAL_STATUS b
	ON a.sjzdctjbm = b.sjzdctjbm  and a.posid = b.posid
where CONVERT(VARCHAR(8), b.Z<PERSON>, 112)>= '${BTIME}' and CONVERT(VARCHAR(8), b.<PERSON>, 112) <'${ETIME}'
GROUP BY a.posid,CONVERT(VARCHAR(8), b.<PERSON>, 112);


--T_PERSONAL_RESERVATION_TOTAL 个人体检项目总表
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'T_PERSONAL_RESERVATION_TOTAL' tbname,
count(*) sjl,
CONVERT(VARCHAR(8), b.ZSSJ, 112) ywrq
from dwd_tj.dbo.T_PERSONAL_RESERVATION_TOTAL a
left join dwd_tj.dbo.T_AUDIT_FINAL_STATUS b
	ON a.sjzdctjbm = b.sjzdctjbm and a.posid = b.posid and a.sjzwybm = b.sjzwybm
where CONVERT(VARCHAR(8), b.ZSSJ, 112)>= '${BTIME}' and CONVERT(VARCHAR(8), b.ZSSJ, 112) <'${ETIME}'
GROUP BY a.posid,CONVERT(VARCHAR(8), b.ZSSJ, 112);



--T_EXAMINATION_QNA_INFO 体检健康问卷采集表
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'T_EXAMINATION_QNA_INFO' tbname,
count(*) sjl,
CONVERT(VARCHAR(8), b.ZSSJ, 112) ywrq
from dwd_tj.dbo.T_EXAMINATION_QNA_INFO a
left join dwd_tj.dbo.T_AUDIT_FINAL_STATUS b
	ON a.sjzdctjbm = b.sjzdctjbm and a.posid = b.posid and a.sjzwybm = b.sjzwybm
where CONVERT(VARCHAR(8), b.ZSSJ, 112)>= '${BTIME}' and CONVERT(VARCHAR(8), b.ZSSJ, 112) <'${ETIME}'
GROUP BY a.posid,CONVERT(VARCHAR(8), b.ZSSJ, 112);


--T_EXAMINATION_BASIC_RESULTS 体检一般检查结果表
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'T_EXAMINATION_BASIC_RESULTS' tbname,
count(*) sjl,
CONVERT(VARCHAR(8), b.ZSSJ, 112) ywrq
from dwd_tj.dbo.T_EXAMINATION_BASIC_RESULTS a
left join dwd_tj.dbo.T_AUDIT_FINAL_STATUS b
	ON a.sjzdctjbm = b.sjzdctjbm and a.posid = b.posid and a.sjzwybm = b.sjzwybm
where CONVERT(VARCHAR(8), b.ZSSJ, 112)>= '${BTIME}' and CONVERT(VARCHAR(8), b.ZSSJ, 112) <'${ETIME}'
GROUP BY a.posid,CONVERT(VARCHAR(8), b.ZSSJ, 112);




--T_EXAMINATION_RESULTS 体检物理检查科室结论表
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'T_EXAMINATION_RESULTS' tbname,
count(*) sjl,
CONVERT(VARCHAR(8), b.ZSSJ, 112) ywrq
from dwd_tj.dbo.T_EXAMINATION_RESULTS a
left join dwd_tj.dbo.T_AUDIT_FINAL_STATUS b
	ON a.sjzdctjbm = b.sjzdctjbm and a.posid = b.posid and a.sjzwybm = b.sjzwybm
where CONVERT(VARCHAR(8), b.ZSSJ, 112)>= '${BTIME}' and CONVERT(VARCHAR(8), b.ZSSJ, 112) <'${ETIME}'
GROUP BY a.posid,CONVERT(VARCHAR(8), b.ZSSJ, 112);



--T_EXAMINATION_RESULTS_DETAIL 体检物理检查结果明细表
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'T_EXAMINATION_RESULTS_DETAIL' tbname,
count(*) sjl,
CONVERT(VARCHAR(8), b.ZSSJ, 112) ywrq
from dwd_tj.dbo.T_EXAMINATION_RESULTS_DETAIL a
left join dwd_tj.dbo.T_AUDIT_FINAL_STATUS b
	ON a.sjzdctjbm = b.sjzdctjbm and a.posid = b.posid and a.sjzwybm = b.sjzwybm
where CONVERT(VARCHAR(8), b.ZSSJ, 112)>= '${BTIME}' and CONVERT(VARCHAR(8), b.ZSSJ, 112) <'${ETIME}'
GROUP BY a.posid,CONVERT(VARCHAR(8), b.ZSSJ, 112);


--T_EXAMINATION_SUPPORTIVE_RESULTS 体检辅助检查放射、超声、内镜、病理、心电图、功能检查等）结果表
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'T_EXAMINATION_SUPPORTIVE_RESULTS' tbname,
count(*) sjl,
CONVERT(VARCHAR(8), b.ZSSJ, 112) ywrq
from dwd_tj.dbo.T_EXAMINATION_SUPPORTIVE_RESULTS a
left join dwd_tj.dbo.T_AUDIT_FINAL_STATUS b
	ON a.sjzdctjbm = b.sjzdctjbm and a.posid = b.posid and a.sjzwybm = b.sjzwybm
where CONVERT(VARCHAR(8), b.ZSSJ, 112)>= '${BTIME}' and CONVERT(VARCHAR(8), b.ZSSJ, 112) <'${ETIME}'
GROUP BY a.posid,CONVERT(VARCHAR(8), b.ZSSJ, 112);



--TB_TJ_LIS_REPORT 体检实验室检验报告表头
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'TB_TJ_LIS_REPORT' tbname,
count(*) sjl,
CONVERT(VARCHAR(8), b.ZSSJ, 112) ywrq
from dwd_tj.dbo.TB_TJ_LIS_REPORT a
left join dwd_tj.dbo.T_AUDIT_FINAL_STATUS b
	ON a.sjzdctjbm = b.sjzdctjbm and a.posid = b.posid and a.sjzwybm = b.sjzwybm
where CONVERT(VARCHAR(8), b.ZSSJ, 112)>= '${BTIME}' and CONVERT(VARCHAR(8), b.ZSSJ, 112) <'${ETIME}'
GROUP BY a.posid,CONVERT(VARCHAR(8), b.ZSSJ, 112);



--TB_TJ_LIS_INDICATORS 体检实验室结果指标表
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'TB_TJ_LIS_INDICATORS' tbname,
count(*) sjl,
CONVERT(VARCHAR(8), b.ZSSJ, 112) ywrq
from dwd_tj.dbo.TB_TJ_LIS_INDICATORS a
left join dwd_tj.dbo.T_AUDIT_FINAL_STATUS b
	ON a.sjzdctjbm = b.sjzdctjbm and a.posid = b.posid and a.sjzwybm = b.sjzwybm
where CONVERT(VARCHAR(8), b.ZSSJ, 112)>= '${BTIME}' and CONVERT(VARCHAR(8), b.ZSSJ, 112) <'${ETIME}'
GROUP BY a.posid,CONVERT(VARCHAR(8), b.ZSSJ, 112);



--T_POSITIVE_AUDIT 重要异常结果记录
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'T_POSITIVE_AUDIT' tbname,
count(*) sjl,
CONVERT(VARCHAR(8), b.ZSSJ, 112) ywrq
from dwd_tj.dbo.T_POSITIVE_AUDIT a
left join dwd_tj.dbo.T_AUDIT_FINAL_STATUS b
	ON a.sjzdctjbm = b.sjzdctjbm and a.posid = b.posid and a.sjzwybm = b.sjzwybm
where CONVERT(VARCHAR(8), b.ZSSJ, 112)>= '${BTIME}' and CONVERT(VARCHAR(8), b.ZSSJ, 112) <'${ETIME}'
GROUP BY a.posid,CONVERT(VARCHAR(8), b.ZSSJ, 112);



--T_AUDIT_FINAL_STATUS 主检报告记录
INSERT into prefix_dq_tj.dbo.tb_sjlmx_ywsj_tj
select 
'db'+substring(a.posid,7,5) dbname,
'T_AUDIT_FINAL_STATUS' tbname,
count(*) sjl,
date_format(a.ZSSJ,'yyyyMMdd') ywrq
from dwd_tj.dbo.T_AUDIT_FINAL_STATUS a
where date_format(a.ZSSJ,'yyyyMMdd')>= '${BTIME}' and date_format(a.ZSSJ,'yyyyMMdd') <'${ETIME}'
GROUP BY a.posid,date_format(a.ZSSJ,'yyyyMMdd');