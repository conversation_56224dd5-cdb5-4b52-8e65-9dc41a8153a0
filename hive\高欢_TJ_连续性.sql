SET hive.execution.engine = tez;
SET yarn.timeline-service.enabled = true;

--T_AUDIT_FINAL_STATUS-连续性 C044
--测量方法  根据业务日期统计当日上传表单，有表单上传填报1（根据业务日期计算上传日数，总上传日数为统计时间范围的天数）


INSERT INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  a.ywrq,
  b.fj_yljgdm,
  'C044',
  nvl(sum(a.sjl),0) as JYJLSH,
  case when nvl(sum(a.sjl),0)=0 then 0 else 1 END as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP as xgshj
from hive_jktj_prod_source_ods.tb_sjlmx_ywsj_tj a
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid b
	ON 'posid_'||substr(a.dbname,3,5) = b.posid and a.tbname='T_AUDIT_FINAL_STATUS' 
	AND a.ywrq >= '${BTIME}' AND a.ywrq < '${ETIME}'
group by a.ywrq,b.fj_yljgdm