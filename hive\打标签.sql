SET hive.execution.engine = tez;
SET yarn.timeline-service.enabled = true;
SET hive.query.timeout.seconds=3600;
INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.T_AUDIT_FINAL_STATUS
SELECT 
    a.yljgmc,a.yljgyqdm,a.sjzdctjbm,a.sjzwybm,a.tjjl,a.j<PERSON>zd,a.cssj,a.c<PERSON><PERSON>yi<PERSON>,a.csysxm,a.zssj,a.z<PERSON>hryid,a.zsysxm,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2')  THEN '1'
        WHEN a.zssj > a.jlgxsj  THEN '1'
        --WHEN day(a.jlgxsj) > 15 and month(a.GTHSJ)!=month(a.jlgxsj)  THEN '1'
        --WHEN day(a.jlgxsj) <= 15 and (month(a.GTHSJ)!=month(a.jlgxsj) and month(a.GTHSJ)!=(month(a.jlgxsj)-1))  THEN '1'
        ELSE 0 
    END AS err_flag,
    -- 错误信息拼接
    CONCAT_WS(
        '&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150)))
            THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') 
            THEN 'E002' 
        END,
        CASE 
            WHEN /*(day(a.jlgxsj) > 15 and month(a.GTHSJ)!=month(a.jlgxsj))
				  or 
				 (day(a.jlgxsj) <= 15 and (month(a.GTHSJ)!=month(a.jlgxsj) and month(a.GTHSJ)!=(month(a.jlgxsj)-1)))
				  or*/
				  a.zssj > a.jlgxsj
            THEN 'E004' 
        END		
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.T_AUDIT_FINAL_STATUS a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.T_PERSONAL_BASE_INFO
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzwybm,a.zjlx,a.zjh,a.tjrxm,a.xb,a.csrq,a.mz,a.jg,a.lxdh,a.jjlxr,a.jjlxrdh,a.txdz,a.email,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.T_PERSONAL_BASE_INFO a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.T_PERSONAL_RESERVATION
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzdctjbm,a.sjzwybm,a.tjlb,a.hyzk,a.whcd,a.yytjrq,a.sjtjrq,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.T_PERSONAL_RESERVATION a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.T_PERSONAL_RESERVATION_TOTAL
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzdctjbm,a.sjzwybm,a.xmlsh,a.tjfldm,a.tjflmc,a.tjksfldm,a.tjksflmc,a.tjxmmx,a.tjxmmc,a.sfyzxm,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.T_PERSONAL_RESERVATION_TOTAL a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.T_EXAMINATION_QNA_INFO
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzdctjbm,a.sjzwybm,a.sss,a.sxs,a.ywgms,a.gmywmc,a.tnbbs,a.tnbhbsc,a.tnbsfksywzl,a.tnbsfzyzasyy,a.tnbjzs,a.gxybs,a.gxyhbsc,a.gxysfksywzl,a.gxysfzyzasyy,a.gxyjzs,a.xzycbs,a.xzychbsc,a.xzycsfksywzl,a.xzycsfzyzasyy,a.xzycjzs,a.xzbbs,a.xzbhbsc,a.xzbsfksywzl,a.xzbsfzyzasyy,a.xzbjzs,a.nxgbbs,a.nxghbsc,a.nxgsfksywzl,a.nxgsfzyzasyy,a.nxgjzs,a.exzlbs,a.exzlhbsc,a.exzlsfksywzl,a.exzlsfzyzasyy,a.exzljzs,a.qtjb,a.xys,a.xysc,a.xyl,a.yjs,a.yjzl,a.ysxg,a.yskw,a.sfjj,a.jjnl,a.jlhdqk,a.sdtlhdqk,a.bxqk,a.nrzzsc,a.xlyldhjz,a.smzl,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.T_EXAMINATION_QNA_INFO a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.T_EXAMINATION_BASIC_RESULTS
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzdctjbm,a.sjzwybm,a.sg,a.tz,a.yw,a.tw,a.ssy,a.szy,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.T_EXAMINATION_BASIC_RESULTS a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.T_EXAMINATION_RESULTS
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzdctjbm,a.sjzwybm,a.ksid,a.ksmc,a.jcyhryid,a.jcysxm,a.jcsj,a.ksjl,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.T_EXAMINATION_RESULTS a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.T_EXAMINATION_RESULTS_DETAIL
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzdctjbm,a.sjzwybm,a.xmlsh,a.ksmc,a.ksid,a.fzjcxmmc,a.fzjcxmbm,a.fzjcxmjg,a.jcyhryid,a.jcysxm,a.jcsj,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.T_EXAMINATION_RESULTS_DETAIL a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.T_EXAMINATION_SUPPORTIVE_RESULTS
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzdctjbm,a.sjzwybm,a.bglsh,a.xmlsh,a.studyuid,a.patient_id,a.mxxmbm,a.mxxmbmyb,a.mxxmmc,a.jcmc,a.jclx,a.jcff,a.bwarc,a.jcyhryid,a.jcysxm,a.jcsj,a.bgyhryid,a.bgysxm,a.bgsj,a.shyhryid,a.shysxm,a.shsj,a.yxbx,a.yxzd,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.T_EXAMINATION_SUPPORTIVE_RESULTS a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.TB_TJ_LIS_REPORT
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzwybm,a.sjzdctjbm,a.bgdh,a.bgsj,a.bgyhryid,a.bgrxm,a.shyhryid,a.shrxm,a.cjsj,a.jysj,a.bbdm,a.bbmc,a.bgdlbbm,a.bgdflmc,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.TB_TJ_LIS_REPORT a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.TB_TJ_LIS_INDICATORS
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzwybm,a.sjzdctjbm,a.bgdh,a.jyzblsh,a.bgsj,a.jcyhryid,a.jcrxm,a.shyhryid,a.shrxm,a.jyzbdm,a.mxxmbm,a.mxxmbmyb,a.mxxmmc,a.jyff,a.jyzbjg,a.jldw,a.ckzfw,a.yctsbm,a.loinc_bm,a.sbbm,a.yqbm,a.yqmc,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.TB_TJ_LIS_INDICATORS a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

INSERT OVERWRITE TABLE hive_jktj_prod_source_ods_interface.T_POSITIVE_AUDIT
SELECT 
	a.yljgmc,a.yljgyqdm,a.sjzdctjbm,a.sjzwybm,a.ywjczyycjg,a.zyycjgnr,a.shclyj,a.shsj,a.shyhryid,a.shysxm,a.yl1,a.yl2,a.yl3,a.yl4,a.yl5,a.yl6,a.xgbz,a.jlscrq,a.jlgxsj,
    -- 错误标志逻辑 (任一条件满足则标记为1)
    CASE 
        WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN '1'
        WHEN a.xgbz NOT IN ('1','2') THEN '1'
        ELSE '0' 
    END AS err_flag,
    
    -- 错误信息动态拼接（去重）
    CONCAT_WS('&',
        CASE 
            WHEN NOT array_contains(b.valid_yljgdms, cast(a.yljgyqdm as varchar(150))) THEN 'E001' 
        END,
        CASE 
            WHEN a.xgbz NOT IN ('1','2') THEN 'E002' 
        END
    ) AS err_note,
    a.serial_no, a.posid
FROM hive_jktj_prod_source_ods_interface.T_POSITIVE_AUDIT a
LEFT JOIN (
    -- 预聚合字典表：每个 posid 对应的有效 yljgdm 集合
		select m.posid,collect_set(n.yljgyqdm) AS valid_yljgdms from hive_wsjk_v12_prod_source_ODS.t_dic_yljgdm_comparison n
		left join hive_wsjk_v12_prod_source_ODS.t_dic_fjyljgdm_posid m
		on n.fj_yljgyqdm=m.fj_yljgdm
		GROUP BY m.posid
) b ON a.posid = b.posid;

