--40035 主检报告记录上传及时性

INSERT INTO  prefix_dq_tj.dbo.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj)
SELECT YWRQ
	,JGDM
	,'40035' as <PERSON><PERSON><PERSON><PERSON>
	,JY<PERSON><PERSON>H as JY<PERSON><PERSON><PERSON>
	,case when FHGZSH>=0 then FHGZSH else 0.00 end as FHGZSH
	,'v2,0' as bbh
	,GETDATE() as XGSHJ
FROM (
	SELECT 
        t1.ZSSJ AS YWRQ,
		t2.FJ_YLJGYQDM AS JGDM,
		COUNT(1) AS JYJLSH,
		sum(round(DATEDIFF(SECOND, t1.ZSSJ, t1.JLGXSJ) * 1.0 / 86400, 2)) AS FHGZSH   --* 1.0 是为了让结果变成小数（否则整数除法会丢失小数部分）
	FROM DWD_tj.dbo.T_AUDIT_FINAL_STATUS t1
	LEFT JOIN prefix_dq_tj.dbo.T_DIC_YLJGDM_COMPARISON t2 ON t1.YLJGYQDM = t2.yljgyqdm
	WHERE t1.XGBZ = '1'
	    --AND t1.bbh = 'v2,0'
		AND CONVERT(VARCHAR(8), t1.ZSSJ, 112) >= '${BTIME}'
		AND CONVERT(VARCHAR(8), t1.ZSSJ, 112) <  '${ETIME}'
	GROUP BY t1.ZSSJ,t2.FJ_YLJGYQDM
	);  
