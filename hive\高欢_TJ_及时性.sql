SET hive.execution.engine = tez;
SET yarn.timeline-service.enabled = true;
SET hive.query.timeout.seconds=3600s;

insert into  hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,jyjlsh,fhgzsh,BBH,xgshj) 
SELECT t.YWRQ  AS YWRQ
	,t.JGDM AS JGDM
	,'40035' AS GZBH
	,t.JYJLSH AS JYJLSH
	,case when t.FHGZSH>=0 then t.FHGZSH else '0.00' end FHGZSH
	,'v2.0' bbh
	,current_timestamp XGSHJ
FROM (
	SELECT DATE_FORMAT(t1.ZSSJ, 'yyyyMMdd') AS YWRQ
		,t2.FJ_YLJGYQDM AS JGDM
		,COUNT(1) AS JYJLSH
		,sum(round((unix_timestamp(t1.jlgxsj) - unix_timestamp(t1.ZSSJ)) / 86400.0, 2)) AS FHGZSH
	FROM hive_jktj_prod_source_dwd.T_AUDIT_FINAL_STATUS t1
	LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.yljgyqdm = t2.yljgyqdm
	WHERE t1.XGBZ = '1'
		    AND DATE_FORMAT(t1.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
		    AND DATE_FORMAT(t1.ZSSJ, 'yyyyMMdd') < '${ETIME}'
	GROUP BY DATE_FORMAT(t1.ZSSJ, 'yyyyMMdd')
		,t2.FJ_YLJGYQDM
	) t;

