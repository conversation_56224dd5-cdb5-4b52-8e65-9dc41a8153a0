UPDATE DWD.dbo.TB_YL_PATIENT_INFORMATION 
SET war_flag = '1',
    war_note = ISNULL(war_note, '') + '&30060'
WHERE (CASE 
                            -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                            WHEN trim(KLX) = '0'
                            AND PATINDEX('[^0-9A-Z]', KH) = 0 
                            AND datalength(KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                            WHEN trim(KLX) = '1'
                            AND PATINDEX('[^0-9]', KH) = 0
                            AND datalength(KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                            WHEN trim(KLX) = '2'
                            AND PATINDEX('[^0-9]', KH) = 0
                            AND datalength(KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                            WHEN trim(KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                            WHEN trim(KLX) = '9' THEN 1 -- 默认情况

    -- 默认情况
    ELSE 0
END)=0
AND (war_note not like '%30060%' OR war_note is NULL) and send_flag='0';



UPDATE DWD.dbo.TB_RIS_PATIENT_INFORMATION 
SET war_flag = '1',
    war_note = ISNULL(war_note, '') + '&30074'
WHERE (
    CASE 
                            -- KLX = '0' 丿KH 昿 使[0-9A-Z]
                            WHEN trim(KLX) = '0'
                            AND PATINDEX('[^0-9A-Z]', KH) = 0 
                            AND datalength(KH) = 9 THEN 1 -- KLX = '1' 丿KH 昿0 位数孿
                            WHEN trim(KLX) = '1'
                            AND PATINDEX('[^0-9]', KH) = 0
                            AND datalength(KH) = 10 THEN 1 -- KLX = '2' 丿KH 昿5 位数孿
                            WHEN trim(KLX) = '2'
                            AND PATINDEX('[^0-9]', KH) = 0
                            AND datalength(KH) = 15 THEN 1 -- KLX = '3' 房4' 丿KH 不为穿
                            WHEN trim(KLX) IN ('3', '4') THEN 1 -- KLX = '9'
                            WHEN trim(KLX) = '9' THEN 1 -- 默认情况

    -- 默认情况
    END
) = 0
AND (war_note not like '%30074%' or war_note is null) AND send_flag='0';