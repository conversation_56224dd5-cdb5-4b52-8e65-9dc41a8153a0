SET hive.execution.engine = tez;
SET yarn.timeline-service.enabled = true;

--40035 主检报告记录上传及时性 error

INSERT INTO  hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL(ywrq,jgdm,gzbh,cwz,cwl,BBH,xgshj)
SELECT YWRQ
	,JGDM
	,'40035' as GZBH
	,cwz
	,coalesce(cwl,0) as cwl
	,'v2,0' as bbh
	,current_timestamp as XGSHJ
FROM (
	SELECT 
		YWRQ,
		JGDM,
		cwz,
		cwl,
		row_number() over(partition by ywrq,jgdm order by cwl desc) as rn
	FROM(
		SELECT 
       		t1.ZSSJ AS YWRQ,
			t2.FJ_YLJGYQDM AS JGDM,
			t1.ZSSJ as cwz,
			COUNT(1) AS cwl
		FROM hive_jktj_prod_source_dwd.T_AUDIT_FINAL_STATUS t1
		LEFT JOIN hive_wsjk_v12_prod_source_ods.T_DIC_YLJGDM_COMPARISON t2 ON t1.YLJGYQDM = t2.yljgyqdm
		WHERE round((unix_timestamp(t1.JLGXSJ) - unix_timestamp(t1.ZSSJ)) / 86400.0, 2) > 1
			and t1.XGBZ = '1'
		AND t1.ZSSJ >= cast(from_unixtime(unix_timestamp('${BTIME}', 'yyyyMMdd')) as timestamp)
		AND t1.ZSSJ < cast(from_unixtime(unix_timestamp('${ETIME}', 'yyyyMMdd')) as timestamp)
		GROUP BY t1.ZSSJ,t2.FJ_YLJGYQDM
	) a
) b
WHERE rn <= 10;

