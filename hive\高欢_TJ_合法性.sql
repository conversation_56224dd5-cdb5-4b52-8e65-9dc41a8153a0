SET hive.execution.engine = tez;
SET yarn.timeline-service.enabled = true;


--E001-v001 T_PERSONAL_BASE_INFO
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzwybm,zjlx,zjh,tjrxm,xb,csrq,mz,jg,lxdh,jjlxr,jjlxrdh,txdz,email,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzwybm  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_personal_base_info )
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  date_format(date_add(a.jlgxsj,-1), 'yyyyMMdd') as ywsj ,
  b.fj_yljgdm,
  'E001-v001',
  count(a.jlgxsj),
  sum(case when instr(a.err_note,'E001')>0 then 0 else 1 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from test a
RIGHT JOIN hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS c
	on a.posid = c.posid and a.SJZWYBM=c.SJZWYBM  
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid b
	ON a.posid = b.posid
where a.xgbz = '1' and a.rn = '1' 
	and date_format(date_add(a.jlgxsj,-1), 'yyyyMMdd') < '${ETIME}' 
	and date_format(date_add(a.jlgxsj,-1), 'yyyyMMdd') >= '${BTIME}'
group by date_format(date_add(a.jlgxsj,-1), 'yyyyMMdd'),b.fj_yljgdm;

--E001-v002 T_PERSONAL_RESERVATION
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzdctjbm,sjzwybm,tjlb,hyzk,whcd,yytjrq,sjtjrq,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzdctjbm, sjzwybm  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_personal_reservation)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  date_format(c.ZSSJ, 'yyyyMMdd') as ywsj ,
  b.fj_yljgdm,
  'E001-v002',
  count(a.jlgxsj),
  sum(case when instr(a.err_note,'E001')>0 then 0 else 1 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from test a
RIGHT JOIN hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS c
	on a.posid = c.posid and a.SJZDCTJBM=c.SJZDCTJBM AND a.SJZWYBM = c.SJZWYBM 
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid b
	ON a.posid = b.posid 
where a.xgbz = '1' and a.rn = '1' 
	AND  date_format(c.ZSSJ, 'yyyyMMdd') < '${ETIME}' 
	and date_format(c.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
group by date_format(c.ZSSJ, 'yyyyMMdd'),b.fj_yljgdm;



--E001-v003 T_PERSONAL_RESERVATION_TOTAL
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzdctjbm,sjzwybm,xmlsh,tjfldm,tjflmc,tjksfldm,tjksflmc,tjxmmx,tjxmmc,sfyzxm,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzdctjbm, sjzwybm, xmlsh  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_personal_reservation_total)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  date_format(c.ZSSJ, 'yyyyMMdd') as ywsj ,
  b.fj_yljgdm,
  'E001-v003',
  count(a.jlgxsj),
  sum(case when instr(a.err_note,'E001')>0 then 0 else 1 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from test a
RIGHT JOIN hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS c
	on a.posid = c.posid and a.SJZDCTJBM=c.SJZDCTJBM AND a.SJZWYBM = c.SJZWYBM 
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid b
	ON a.posid = b.posid 
where a.xgbz = '1' and a.rn = '1'
	AND  date_format(c.ZSSJ, 'yyyyMMdd') < '${ETIME}' 
	and date_format(c.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
group by date_format(c.ZSSJ, 'yyyyMMdd'),b.fj_yljgdm;


--E001-v004 T_EXAMINATION_QNA_INFO
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzdctjbm,sjzwybm,sss,sxs,ywgms,gmywmc,tnbbs,tnbhbsc,tnbsfksywzl,tnbsfzyzasyy,tnbjzs,gxybs,gxyhbsc,gxysfksywzl,gxysfzyzasyy,gxyjzs,xzycbs,xzychbsc,xzycsfksywzl,xzycsfzyzasyy,xzycjzs,xzbbs,xzbhbsc,xzbsfksywzl,xzbsfzyzasyy,xzbjzs,nxgbbs,nxghbsc,nxgsfksywzl,nxgsfzyzasyy,nxgjzs,exzlbs,exzlhbsc,exzlsfksywzl,exzlsfzyzasyy,exzljzs,qtjb,xys,xysc,xyl,yjs,yjzl,ysxg,yskw,sfjj,jjnl,jlhdqk,sdtlhdqk,bxqk,nrzzsc,xlyldhjz,smzl,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzdctjbm, sjzwybm  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_examination_qna_info)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  date_format(c.ZSSJ, 'yyyyMMdd') as ywsj ,
  b.fj_yljgdm,
  'E001-v004',
  count(a.jlgxsj),
  sum(case when instr(a.err_note,'E001')>0 then 0 else 1 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from test a
RIGHT JOIN hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS c
	on a.posid = c.posid and a.SJZDCTJBM=c.SJZDCTJBM AND a.SJZWYBM = c.SJZWYBM 
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid b
	ON a.posid = b.posid 
where a.xgbz = '1' and a.rn = '1'
	AND  date_format(c.ZSSJ, 'yyyyMMdd') < '${ETIME}' 
	and date_format(c.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
group by date_format(c.ZSSJ, 'yyyyMMdd'),b.fj_yljgdm;


--E001-v005 T_EXAMINATION_BASIC_RESULTS
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzdctjbm,sjzwybm,sg,tz,yw,tw,ssy,szy,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzdctjbm, sjzwybm  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_examination_basic_results)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  date_format(c.ZSSJ, 'yyyyMMdd') as ywsj ,
  b.fj_yljgdm,
  'E001-v005',
  count(a.jlgxsj),
  sum(case when instr(a.err_note,'E001')>0 then 0 else 1 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from test a
RIGHT JOIN hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS c
	on a.posid = c.posid and a.SJZDCTJBM=c.SJZDCTJBM AND a.SJZWYBM = c.SJZWYBM 
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid b
	ON a.posid = b.posid 
where a.xgbz = '1' and a.rn = '1'
	AND  date_format(c.ZSSJ, 'yyyyMMdd') < '${ETIME}' 
	and date_format(c.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
group by date_format(c.ZSSJ, 'yyyyMMdd'),b.fj_yljgdm;


--E001-v006 T_EXAMINATION_RESULTS
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzdctjbm,sjzwybm,ksid,ksmc,jcyhryid,jcysxm,jcsj,ksjl,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzdctjbm, sjzwybm, ksid  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_examination_results)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  date_format(c.ZSSJ, 'yyyyMMdd') as ywsj ,
  b.fj_yljgdm,
  'E001-v006',
  count(a.jlgxsj),
  sum(case when instr(a.err_note,'E001')>0 then 0 else 1 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from test a
RIGHT JOIN hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS c
	on a.posid = c.posid and a.SJZDCTJBM=c.SJZDCTJBM AND a.SJZWYBM = c.SJZWYBM 
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid b
	ON a.posid = b.posid 
where a.xgbz = '1' and a.rn = '1'
	AND  date_format(c.ZSSJ, 'yyyyMMdd') < '${ETIME}' 
	and date_format(c.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
group by date_format(c.ZSSJ, 'yyyyMMdd'),b.fj_yljgdm;


--E001-v007 T_EXAMINATION_RESULTS_DETAIL
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzdctjbm,sjzwybm,xmlsh,ksmc,ksid,fzjcxmmc,fzjcxmbm,fzjcxmjg,jcyhryid,jcysxm,jcsj,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzdctjbm, sjzwybm, xmlsh  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_examination_results_detail)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  date_format(c.ZSSJ, 'yyyyMMdd') as ywsj ,
  b.fj_yljgdm,
  'E001-v007',
  count(a.jlgxsj),
  sum(case when instr(a.err_note,'E001')>0 then 0 else 1 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from test a
RIGHT JOIN hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS c
	on a.posid = c.posid and a.SJZDCTJBM=c.SJZDCTJBM AND a.SJZWYBM = c.SJZWYBM 
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid b
	ON a.posid = b.posid 
where a.xgbz = '1' and a.rn = '1'
	AND  date_format(c.ZSSJ, 'yyyyMMdd') < '${ETIME}' 
	and date_format(c.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
group by date_format(c.ZSSJ, 'yyyyMMdd'),b.fj_yljgdm;


--E001-v008 T_EXAMINATION_SUPPORTIVE_RESULTS
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzdctjbm,sjzwybm,bglsh,xmlsh,studyuid,patient_id,mxxmbm,mxxmbmyb,mxxmmc,jcmc,jclx,jcff,bwarc,jcyhryid,jcysxm,jcsj,bgyhryid,bgysxm,bgsj,shyhryid,shysxm,shsj,yxbx,yxzd,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzdctjbm, sjzwybm, bglsh, xmlsh  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_examination_supportive_results)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  date_format(c.ZSSJ, 'yyyyMMdd') as ywsj ,
  b.fj_yljgdm,
  'E001-v008',
  count(a.jlgxsj),
  sum(case when instr(a.err_note,'E001')>0 then 0 else 1 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from test a
RIGHT JOIN hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS c
	on a.posid = c.posid and a.SJZDCTJBM=c.SJZDCTJBM AND a.SJZWYBM = c.SJZWYBM 
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid b
	ON a.posid = b.posid 
	where a.xgbz = '1' and a.rn = '1'
	AND  date_format(c.ZSSJ, 'yyyyMMdd') < '${ETIME}' 
	and date_format(c.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
group by date_format(c.ZSSJ, 'yyyyMMdd'),b.fj_yljgdm;


--E001-v009 TB_TJ_LIS_REPORT
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzwybm,sjzdctjbm,bgdh,bgsj,bgyhryid,bgrxm,shyhryid,shrxm,cjsj,jysj,bbdm,bbmc,bgdlbbm,bgdflmc,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzwybm, sjzdctjbm, bgdh  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.tb_tj_lis_report)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  date_format(c.ZSSJ, 'yyyyMMdd') as ywsj ,
  b.fj_yljgdm,
  'E001-v009',
  count(a.jlgxsj),
  sum(case when instr(a.err_note,'E001')>0 then 0 else 1 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from test a
RIGHT JOIN hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS c
	on a.posid = c.posid and a.SJZDCTJBM=c.SJZDCTJBM AND a.SJZWYBM = c.SJZWYBM 
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid b
ON a.posid = b.posid  
	where a.xgbz = '1' and a.rn = '1'
	AND  date_format(c.ZSSJ, 'yyyyMMdd') < '${ETIME}' 
	and date_format(c.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
group by date_format(c.ZSSJ, 'yyyyMMdd'),b.fj_yljgdm;





--E099-v008 T_EXAMINATION_SUPPORTIVE_RESULTS-重大错误数据
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzdctjbm,sjzwybm,bglsh,xmlsh,studyuid,patient_id,mxxmbm,mxxmbmyb,mxxmmc,jcmc,jclx,jcff,bwarc,jcyhryid,jcysxm,jcsj,bgyhryid,bgysxm,bgsj,shyhryid,shysxm,shsj,yxbx,yxzd,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzdctjbm, sjzwybm, bglsh, xmlsh  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_examination_supportive_results  )
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  DATE_FORMAT(a.ZSSJ, 'yyyyMMdd'),
  c.fj_yljgdm,
  'E099-v008',
  count(a.jlgxsj),
  sum(case when a.err_flag='0' then 1 else 0 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS a
RIGHT JOIN test  b
    on a.posid = b.posid and a.SJZDCTJBM = b.SJZDCTJBM and a.SJZWYBM = b.SJZWYBM
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid c
    ON a.posid = c.posid 
where  b.xgbz = '1' and b.rn = '1'
	AND DATE_FORMAT(a.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
	AND DATE_FORMAT(a.ZSSJ, 'yyyyMMdd') < '${ETIME}'
group by DATE_FORMAT(a.ZSSJ, 'yyyyMMdd'),c.fj_yljgdm;


-- E099-v009 TB_TJ_LIS_REPORT-重大错误数据
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzwybm,sjzdctjbm,bgdh,bgsj,bgyhryid,bgrxm,shyhryid,shrxm,cjsj,jysj,bbdm,bbmc,bgdlbbm,bgdflmc,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzwybm, sjzdctjbm, bgdh  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.tb_tj_lis_report  )
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  DATE_FORMAT(a.ZSSJ, 'yyyyMMdd'),
  c.fj_yljgdm,
  'E099-v009',
  count(a.jlgxsj),
  sum(case when a.err_flag='0' then 1 else 0 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS a
RIGHT JOIN test  b
    on a.posid = b.posid and a.SJZDCTJBM = b.SJZDCTJBM and a.SJZWYBM = b.SJZWYBM
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid c
    ON a.posid = c.posid 
where  b.xgbz = '1' and b.rn = '1'
	AND DATE_FORMAT(a.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
	AND DATE_FORMAT(a.ZSSJ, 'yyyyMMdd') < '${ETIME}'
group by DATE_FORMAT(a.ZSSJ, 'yyyyMMdd'),c.fj_yljgdm;


-- E099-v010 TB_TJ_LIS_INDICATORS-重大错误数据
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzwybm,sjzdctjbm,bgdh,jyzblsh,bgsj,jcyhryid,jcrxm,shyhryid,shrxm,jyzbdm,mxxmbm,mxxmbmyb,mxxmmc,jyff,jyzbjg,jldw,ckzfw,yctsbm,loinc_bm,sbbm,yqbm,yqmc,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzwybm, sjzdctjbm, bgdh, jyzblsh  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.tb_tj_lis_indicators)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  DATE_FORMAT(a.ZSSJ, 'yyyyMMdd'),
  c.fj_yljgdm,
  'E099-v010',
  count(a.jlgxsj),
  sum(case when a.err_flag='0' then 1 else 0 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS a
RIGHT JOIN test  b
    on a.posid = b.posid and a.SJZDCTJBM = b.SJZDCTJBM and a.SJZWYBM = b.SJZWYBM
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid c
    ON a.posid = c.posid
where  b.xgbz = '1' and b.rn = '1'	
	AND DATE_FORMAT(a.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
	AND DATE_FORMAT(a.ZSSJ, 'yyyyMMdd') < '${ETIME}'
group by DATE_FORMAT(a.ZSSJ, 'yyyyMMdd'),c.fj_yljgdm;


-- E099-v011 T_POSITIVE_AUDIT-重大错误数据
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzdctjbm,sjzwybm,ywjczyycjg,zyycjgnr,shclyj,shsj,shyhryid,shysxm,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzdctjbm, sjzwybm  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_positive_audit)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  DATE_FORMAT(a.ZSSJ, 'yyyyMMdd'),
  c.fj_yljgdm,
  'E099-v011',
  count(a.jlgxsj),
  sum(case when a.err_flag='0' then 1 else 0 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from hive_jktj_prod_source_ods.T_AUDIT_FINAL_STATUS a
RIGHT JOIN test  b
    on a.posid = b.posid and a.SJZDCTJBM = b.SJZDCTJBM and a.SJZWYBM = b.SJZWYBM
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid c
    ON a.posid = c.posid 
where  b.xgbz = '1' and b.rn = '1'
	AND DATE_FORMAT(a.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
	AND DATE_FORMAT(a.ZSSJ, 'yyyyMMdd') < '${ETIME}'
group by DATE_FORMAT(a.ZSSJ, 'yyyyMMdd'),c.fj_yljgdm;


-- E099-v012 T_AUDIT_FINAL_STATUS-重大错误数据
WITH test AS ( SELECT  yljgmc,yljgyqdm,sjzdctjbm,sjzwybm,tjjl,jyzd,cssj,csyhryid,csysxm,zssj,zsyhryid,zsysxm,yl1,yl2,yl3,yl4,yl5,yl6,xgbz,jlscrq,jlgxsj,err_flag,err_note,serial_no,posid   ,ROW_NUMBER() OVER (PARTITION BY  yljgyqdm, sjzdctjbm, sjzwybm  ORDER BY jlgxsj DESC) AS rn  FROM hive_jktj_prod_source_ods.t_audit_final_status)
insert INTO hive_jktj_prod_source_dwd.TB_DQ_INDICATOR_YWSJ_DETAIL 
select
  DATE_FORMAT(a.ZSSJ, 'yyyyMMdd'),
  c.fj_yljgdm,
  'E099-v012',
  count(a.jlgxsj),
  sum(case when a.err_flag='0' then 1 else 0 end) as FHGZSH,
  'v2.0',
  CURRENT_TIMESTAMP
from test a
RIGHT JOIN hive_wsjk_v12_prod_source_ods.t_dic_fjyljgdm_posid c
    ON a.posid = c.posid 
where  a.xgbz = '1' and a.rn = '1'
	AND DATE_FORMAT(a.ZSSJ, 'yyyyMMdd') >= '${BTIME}'
	AND DATE_FORMAT(a.ZSSJ, 'yyyyMMdd') < '${ETIME}'
group by DATE_FORMAT(a.ZSSJ, 'yyyyMMdd'),c.fj_yljgdm;