
update dwd.TB_STAT_YWL_REPORT
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40001'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - TO_TIMESTAMP(ywrq || '235959', 'YYYYMMDDHH24MISS')) ) / 86400.0, 2) > 1
    AND XGBZ = '1'
  AND YWRQ >= '${BTIME}'
  AND YWRQ < '${ETIME}'
  AND (war_note not like '%40001%' or war_note is null) AND send_flag='0';

update dwd.tb_stat_ywsr_report
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40002'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - TO_TIMESTAMP(ywrq || '235959', 'YYYYMMDDHH24MISS'))) / 86400.0, 2) > 1
    AND XGBZ = '1'
  AND YWRQ >= '${BTIME}'
  AND YWRQ < '${ETIME}'
  AND (war_note not like '%40002%' or war_note is null) AND send_flag='0';

update dwd.tb_stat_lis_report
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40003'
 WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - TO_TIMESTAMP(ywrq || '235959', 'YYYYMMDDHH24MISS'))) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND YWRQ >= '${BTIME}'
  AND YWRQ < '${ETIME}'
  AND (war_note not like '%40003%' or war_note is null) AND send_flag='0';

update dwd.tb_stat_ris_report
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40004'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - TO_TIMESTAMP(ywrq || '235959', 'YYYYMMDDHH24MISS'))) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND YWRQ >= '${BTIME}'
  AND YWRQ < '${ETIME}'
  AND (war_note not like '%40004%' or war_note is null) AND send_flag='0';

update dwd.tb_his_mz_reg
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40005'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - GTHSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(GTHSJ, 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(GTHSJ, 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40005%' or war_note is null) AND send_flag='0';


update dwd.tb_his_mz_charge
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40006'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - STFSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(STFSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(STFSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40006%' or war_note is null) AND send_flag='0';

update dwd.tb_his_zy_adm_reg
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40007'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - RYSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(RYSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(RYSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40007%' or war_note is null) AND send_flag='0';


update dwd.tb_his_zy_dis_reg
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40008'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - CYSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(CYSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(CYSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40008%' or war_note is null) AND send_flag='0';

update dwd.tb_his_jz_charge
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40009'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - STFSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(STFSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(STFSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40009%' or war_note is null) AND send_flag='0';


update dwd.tb_yl_mz_medical_record
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40010'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - JZKSRQ )) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(JZKSRQ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(JZKSRQ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40010%' or war_note is null) AND send_flag='0';

update dwd.tb_cis_prescription_detail
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40011'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - KFRQ )) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(KFRQ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(KFRQ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40011%' or war_note is null) AND send_flag='0';


update dwd.tb_his_mz_fee_detail
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40012'
 WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - STFSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(STFSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(STFSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40012%' or war_note is null) AND send_flag='0';


update dwd.tb_yl_zy_medical_record
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40013'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - CYSJ)) / 86400.0, 2) > 1
   AND XGBZ = '1'
  AND TO_CHAR(CYSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(CYSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40013%' or war_note is null) AND send_flag='0';


update dwd.tb_cis_dradvice_detail
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40014'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - SHSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40014%' or war_note is null) AND send_flag='0';


update dwd.tb_his_zy_fee_detail
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40015'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - STFSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(STFSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(STFSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40015%' or war_note is null) AND send_flag='0';


update dwd.tb_lis_report
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40016'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - SHSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40016%' or war_note is null) AND send_flag='0';


update dwd.tb_lis_report
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40017'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - SHSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40017%' or war_note is null) AND send_flag='0';


update dwd.tb_lis_bacteria_result
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40018'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - SHSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40018%' or war_note is null) AND send_flag='0';


update dwd.tb_lis_allergy_result
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40019'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - SHSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40019%' or war_note is null) AND send_flag='0';


update dwd.tb_ris_report
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40020'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - SHSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40020%' or war_note is null) AND send_flag='0';


update dwd.tb_ris_report2
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40021'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - SHSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40021%' or war_note is null) AND send_flag='0';


update dwd.tb_opration_detail
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40022'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - SSJSSJ )) / 86400.0, 2) > 1
   AND XGBZ = '1'
  AND TO_CHAR(SSJSSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SSJSSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40022%' or war_note is null) AND send_flag='0';


update dwd.tb_ih_diagnosis_detail
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40023'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - ZDSJ )) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(ZDSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(ZDSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40023%' or war_note is null) AND send_flag='0';


update dwd.tb_cis_leavehospital_summary
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40024'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - CYSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(CYSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(CYSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40024%' or war_note is null) AND send_flag='0';


update dwd.tb_his_zy_fee_detail_fs
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40025'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - FYFSSJ )) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(FYFSSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(FYFSSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40025%' or war_note is null) AND send_flag='0';


update dwd.tb_his_opr_rec
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40026'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - SSJSSJ )) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SSJSSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SSJSSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40026%' or war_note is null) AND send_flag='0';


update dwd.tb_his_appointment
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40027'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - SJJZSJ )) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SJJZSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SJJZSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40027%' or war_note is null) AND send_flag='0';


update dwd.TB_STAT_APPOINTMENT_REPORT
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40028'
WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj -TO_TIMESTAMP(ywrq || '235959', 'YYYYMMDDHH24MISS'))) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND ywrq >= '${BTIME}'
  AND ywrq < '${ETIME}'
  AND (war_note not like '%40028%' or war_note is null) AND send_flag='0';


update dwd.TB_RIS_REPORT_DETAIL
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40029'
 WHERE ROUND(EXTRACT(EPOCH FROM (jlgxsj - SHSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40029%' or war_note is null) AND send_flag='0';


update dwd.tb_ba_syjbk
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40030'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - GDRQ )) / 86400.0, 2) > 1
  AND TO_CHAR(GDRQ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(GDRQ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40030%' or war_note is null) AND send_flag='0';


update dwd.tb_ba_syzdk
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40031'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - GDRQ )) / 86400.0, 2) > 1
  AND TO_CHAR(GDRQ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(GDRQ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40031%' or war_note is null) AND send_flag='0';


update dwd.tb_ba_syssk
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40032'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - GDRQ )) / 86400.0, 2) > 1
  AND TO_CHAR(GDRQ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(GDRQ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40032%' or war_note is null) AND send_flag='0';


update dwd.tb_ba_syyek
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40033'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - GDRQ )) / 86400.0, 2) > 1
  AND TO_CHAR(GDRQ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(GDRQ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40033%' or war_note is null) AND send_flag='0';



update dwd.tb_genetic_report
set war_flag = '1',
    war_note = COALESCE(war_note, '') || '&40034'
WHERE ROUND(EXTRACT(EPOCH FROM ( jlgxsj - SHSJ)) / 86400.0, 2) > 1
  AND XGBZ = '1'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') >= '${BTIME}'
  AND TO_CHAR(SHSJ , 'YYYYMMDD') < '${ETIME}'
  AND (war_note not like '%40034%' or war_note is null) AND send_flag='0';




